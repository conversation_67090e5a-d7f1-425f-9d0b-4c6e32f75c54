# User Activity Logging Service Guide

## ✅ Task 4 Complete: Centralized Logging Service Created

### **What We've Built:**

#### **1. UserActivityLogger Class** (`user_activity_logger.py`)
- **Complete logging service** for all user activities
- **Error handling** with graceful degradation
- **Both authenticated and anonymous** user support
- **Database session management** with proper cleanup
- **Auto-categorization** of queries and interactions

#### **2. SessionManager Class** (`session_manager.py`)
- **Session tracking** for engagement analysis
- **Cookie-based session management** for anonymous users
- **Browser and device detection** from user agents
- **IP address extraction** with proxy header support
- **Session lifecycle management** (start, update, end)

#### **3. Helper Functions & Middleware**
- **Global logger instances** for easy integration
- **Session tracking middleware** for automatic session management
- **Convenience methods** for common logging operations
- **Request/response integration** with FastAPI

## **Core Logging Capabilities**

### **Query Logging**
```python
from user_activity_logger import log_query_activity

# Log a successful query
query_id = log_query_activity(
    query_text="pizza tilbud",
    results_count=15,
    response_time_ms=250,
    was_successful=True,
    user_id=user.id if user else None,
    session_id=session_id,
    selected_stores=[1, 2, 3],
    query_model="gemini-2.5-flash-lite"
)

# Log a failed query
query_id = log_query_activity(
    query_text="invalid query",
    results_count=0,
    response_time_ms=500,
    was_successful=False,
    error_message="No products found",
    user_id=user.id if user else None,
    session_id=session_id
)
```

### **Session Tracking**
```python
from session_manager import track_page_view, increment_session_query_count

# Track page view and get session ID
session_id = track_page_view(request, response, user_id=user.id if user else None)

# Increment query count for session
increment_session_query_count(session_id)
```

### **Interaction Logging**
```python
from user_activity_logger import log_user_interaction

# Log catalog view
interaction_id = log_user_interaction(
    interaction_type="catalog_viewed",
    user_id=user.id if user else None,
    session_id=session_id,
    target_type="catalog",
    target_id=catalog_id,
    source_query_id=query_id,
    interaction_data={"duration_seconds": 30}
)

# Log product click
interaction_id = log_user_interaction(
    interaction_type="product_clicked",
    user_id=user.id if user else None,
    session_id=session_id,
    target_type="product",
    target_id=product_id,
    source_query_id=query_id
)
```

### **Preference Change Logging**
```python
from user_activity_logger import activity_logger

# Log user preference change
success = activity_logger.log_preference_change(
    user_id=user.id,
    preference_type="preferred_stores",
    old_value="[1, 2]",
    new_value="[1, 2, 3]",
    change_reason="user_selection"
)
```

### **Admin Audit Logging**
```python
# Log admin access to user data
success = activity_logger.log_admin_action(
    admin_user_id=admin.id,
    action_type="user_viewed",
    target_user_id=target_user.id,
    resource_accessed="query_history",
    action_details={"queries_viewed": 50},
    ip_address=request.client.host,
    user_agent=request.headers.get("user-agent")
)
```

## **Integration with API Endpoints**

### **Query Endpoint Integration**
```python
from fastapi import FastAPI, Request, Response, Depends
from user_activity_logger import log_query_activity
from session_manager import track_page_view, increment_session_query_count
import time

@app.post("/ask")
async def ask_question(
    request: Request,
    response: Response,
    query_data: dict,
    current_user = Depends(try_get_current_user)
):
    start_time = time.time()
    
    # Track session and get session ID
    session_id = track_page_view(request, response, 
                                user_id=current_user.id if current_user else None)
    
    try:
        # Process the query
        query_text = query_data.get("query", "")
        selected_stores = query_data.get("selected_stores", [])
        
        # Your existing query processing logic here...
        results = process_query(query_text, selected_stores)
        
        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Log successful query
        query_id = log_query_activity(
            query_text=query_text,
            results_count=len(results),
            response_time_ms=response_time_ms,
            was_successful=True,
            user_id=current_user.id if current_user else None,
            session_id=session_id,
            selected_stores=selected_stores,
            query_model="gemini-2.5-flash-lite"
        )
        
        # Increment session query count
        increment_session_query_count(session_id)
        
        return {"results": results, "query_id": query_id}
        
    except Exception as e:
        # Calculate response time for failed query
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Log failed query
        log_query_activity(
            query_text=query_text,
            results_count=0,
            response_time_ms=response_time_ms,
            was_successful=False,
            error_message=str(e),
            user_id=current_user.id if current_user else None,
            session_id=session_id,
            selected_stores=selected_stores
        )
        
        raise e
```

### **Catalog View Endpoint**
```python
@app.get("/catalog/{catalog_id}")
async def view_catalog(
    catalog_id: int,
    request: Request,
    response: Response,
    current_user = Depends(try_get_current_user)
):
    # Track session
    session_id = track_page_view(request, response, 
                                user_id=current_user.id if current_user else None)
    
    # Get source query ID from query parameters (if available)
    source_query_id = request.query_params.get("query_id")
    
    # Log catalog view interaction
    log_user_interaction(
        interaction_type="catalog_viewed",
        user_id=current_user.id if current_user else None,
        session_id=session_id,
        target_type="catalog",
        target_id=catalog_id,
        source_query_id=int(source_query_id) if source_query_id else None,
        page_url=str(request.url)
    )
    
    # Your existing catalog logic here...
    return get_catalog_data(catalog_id)
```

## **Auto-Categorization Features**

### **Query Categorization**
The logging service automatically categorizes queries:
```python
from user_activity_constants import categorize_query, detect_query_intent

# Automatic categorization
category = categorize_query("pizza tilbud")  # Returns: "food_beverages"
intent = detect_query_intent("billigste øl", 3)  # Returns: "price_comparison"
```

### **Device Detection**
```python
from user_activity_constants import get_device_type_from_user_agent

device_type = get_device_type_from_user_agent(
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)"
)  # Returns: "mobile"
```

## **Error Handling & Resilience**

### **Graceful Degradation**
- **Logging failures don't break the application**
- **Warnings logged for debugging** but app continues
- **Database errors handled** with rollback
- **Invalid data validated** with warnings

### **Performance Considerations**
- **Async-compatible** design
- **Database session pooling** support
- **Minimal overhead** on main application logic
- **Batch operations** where possible

## **Testing the Logging Service**

### **Basic Functionality Test**
```python
from user_activity_logger import UserActivityLogger
from database import get_db

# Test basic query logging
logger = UserActivityLogger()

query_id = logger.log_query(
    query_text="test query",
    user_id=1,
    session_id="test_session",
    selected_stores=[1, 2],
    results_count=5,
    response_time_ms=200,
    was_successful=True
)

print(f"Query logged with ID: {query_id}")
```

### **Session Management Test**
```python
from session_manager import SessionManager

session_mgr = SessionManager()

# Test session lifecycle
session_id = "test_session_123"
session_mgr.start_session(session_id, request, user_id=1)
session_mgr.end_session(session_id, queries_count=3, interactions_count=5)
```

## **Next Steps for Integration**

### **Task 5: Integrate Query Logging into API Endpoints**
- Update `/ask` endpoint to log all queries
- Add session tracking to all endpoints
- Implement error logging for failed queries

### **Task 6: Add Session Tracking Middleware**
- Create FastAPI middleware for automatic session tracking
- Handle session cookies and user identification
- Track page views and navigation patterns

### **Task 7: Implement Interaction Logging**
- Add logging to catalog view endpoints
- Track product clicks and engagement
- Log store selection changes

## **Logging Service Status: ✅ COMPLETE**

The comprehensive user activity logging service is now ready for integration into your API endpoints. It provides:

- ✅ **Complete query tracking** with auto-categorization
- ✅ **Session management** for engagement analysis
- ✅ **Interaction logging** for user behavior insights
- ✅ **Admin audit trails** for GDPR compliance
- ✅ **Error handling** with graceful degradation
- ✅ **Performance optimization** with minimal overhead

**Ready to integrate into your API endpoints!** 🚀
