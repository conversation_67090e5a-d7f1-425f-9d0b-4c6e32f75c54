"use client";

import { useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "lib/auth";

// Simple card component for dashboard links
const DashboardCard = ({
  title,
  description,
  href,
}: {
  title: string;
  description: string;
  href: string;
}) => (
  <Link
    href={href}
    className="block p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700 transition-colors"
  >
    <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
      {title}
    </h3>
    <p className="text-gray-700 dark:text-gray-400 text-sm leading-relaxed">
      {description}
    </p>
  </Link>
);

const AdminDashboard = () => {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  // Redirect non-admin or unauthenticated users to login page
  useEffect(() => {
    if (!isLoading && (!user || !user.is_admin)) {
      router.push("/login");
    }
  }, [isLoading, user, router]);

  if (isLoading || !user || !user.is_admin) {
    return <p className="text-center py-10">Indlæser...</p>;
  }

  return (
    <div className="max-w-5xl mx-auto py-10 px-4 space-y-8">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
        Admin Dashboard
      </h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <DashboardCard
          title="Kataloger"
          description="Upload, se og slet tilbudsaviser."
          href="/admin/catalogs"
        />
        <DashboardCard
          title="Butikker"
          description="Administrér butikker og deres oplysninger."
          href="/admin/stores"
        />
        <DashboardCard
          title="Brugere"
          description="Se og administrér brugerkonti og roller."
          href="/admin/users"
        />
        <DashboardCard
          title="📊 User Activity Dashboard"
          description="Real-time user behavior analytics, session tracking, and comprehensive activity monitoring with live statistics."
          href="/admin/user-activity"
        />
        <DashboardCard
          title="Indstillinger"
          description="Konfigurer globale app-indstillinger."
          href="/admin/settings"
        />
        <DashboardCard
          title="🧪 Parser Test"
          description="Test parser with different images and settings in real-time."
          href="/admin/parser-test"
        />
        <DashboardCard
          title="🏗️ Enterprise Pipeline Dashboard"
          description="Real-time monitoring, operation logs, manual controls (scraping & parsing), and enterprise-grade status tracking for million-dollar SaaS operations."
          href="/admin/processing"
        />
        <DashboardCard
          title="🧪 Test Site"
          description="Eksperimentel playground med nye UI-funktioner og animationer."
          href="/test"
        />
      </div>
    </div>
  );
};

export default AdminDashboard;
