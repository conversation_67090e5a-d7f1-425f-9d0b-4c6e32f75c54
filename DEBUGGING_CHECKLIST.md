# Debugging Checklist & Troubleshooting Guide

## 🔍 **Step-by-Step Debugging Process**

Use this checklist to systematically test and debug the user activity tracking system on your local server.

---

## 📋 **Pre-Flight Checklist**

### **1. Environment Setup**
- [ ] Python 3.8+ installed and activated
- [ ] PostgreSQL database accessible
- [ ] All required Python packages installed
- [ ] Environment variables configured
- [ ] FastAPI server can start without errors

### **2. Database Verification**
```bash
# Check database connection
python -c "
from database import engine
try:
    with engine.connect() as conn:
        print('✅ Database connection successful')
        result = conn.execute('SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = \\'public\\'')
        print(f'✅ Found {result.fetchone()[0]} tables in database')
except Exception as e:
    print(f'❌ Database error: {e}')
"
```

### **3. Migration Status**
```bash
# Check if user activity tables exist
python -c "
from database import engine
with engine.connect() as conn:
    result = conn.execute(\"\"\"
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name LIKE 'user_%'
        ORDER BY table_name
    \"\"\")
    tables = [row[0] for row in result.fetchall()]
    expected = ['user_interactions', 'user_preferences_history', 'user_queries', 'user_sessions']
    
    print('Expected tables:', expected)
    print('Found tables:', tables)
    
    missing = set(expected) - set(tables)
    if missing:
        print(f'❌ Missing tables: {missing}')
        print('Run: alembic upgrade head')
    else:
        print('✅ All user activity tables found')
"
```

---

## 🚀 **Backend Testing**

### **Step 1: FastAPI Server**
```bash
# Start server with debug logging
uvicorn api:app --reload --host 0.0.0.0 --port 8000 --log-level debug

# Expected output:
# INFO:     Uvicorn running on http://0.0.0.0:8000
# INFO:     Application startup complete
```

**Troubleshooting:**
- If import errors: Check all new files are in the correct directory
- If database errors: Verify DATABASE_URL in environment
- If middleware errors: Check session_tracking_middleware.py syntax

### **Step 2: Session Middleware**
```bash
# Test session creation
curl -v -c cookies.txt http://localhost:8000/stores

# Check response headers for Set-Cookie
# Should see: Set-Cookie: tilbudsjaegeren_session=...

# Verify session in database
python -c "
from database import get_db, UserSession
db = next(get_db())
sessions = db.query(UserSession).count()
print(f'✅ Sessions in database: {sessions}')
if sessions == 0:
    print('❌ No sessions created - check middleware')
db.close()
"
```

### **Step 3: Query Logging**
```bash
# Test query endpoint
curl -X POST http://localhost:8000/ask \
  -H "Content-Type: application/json" \
  -d '{"query": "test pizza", "selected_stores": [1, 2, 3]}'

# Verify query was logged
python -c "
from database import get_db, UserQuery
db = next(get_db())
queries = db.query(UserQuery).filter(UserQuery.query_text.like('%test pizza%')).count()
print(f'✅ Test queries found: {queries}')
if queries == 0:
    print('❌ Query not logged - check user_activity_logger.py')
db.close()
"
```

### **Step 4: Interaction Logging**
```bash
# Test interaction endpoint
curl -X POST http://localhost:8000/log-product-click \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 999,
    "product_name": "Debug Test Product",
    "store_id": 1,
    "store_name": "Test Store"
  }'

# Verify interaction was logged
python -c "
from database import get_db, UserInteraction
db = next(get_db())
interactions = db.query(UserInteraction).filter(
    UserInteraction.target_id == 999
).count()
print(f'✅ Test interactions found: {interactions}')
if interactions == 0:
    print('❌ Interaction not logged - check advanced_interaction_logger.py')
db.close()
"
```

---

## 👤 **Admin System Testing**

### **Step 1: Create Admin User**
```python
# Create admin user for testing
from database import get_db, User
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
db = next(get_db())

# Check if admin exists
admin = db.query(User).filter(User.username == "<EMAIL>").first()
if not admin:
    admin = User(
        username="<EMAIL>",
        hashed_password=pwd_context.hash("debug123"),
        is_admin=True
    )
    db.add(admin)
    db.commit()
    print(f"✅ Admin user created: {admin.username}")
else:
    print(f"✅ Admin user exists: {admin.username}")

db.close()
```

### **Step 2: Get Admin Token**
```bash
# Login and get token
RESPONSE=$(curl -s -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "debug123"}')

echo "Login response: $RESPONSE"

# Extract token (adjust based on your auth response format)
TOKEN=$(echo $RESPONSE | python -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('access_token', 'TOKEN_NOT_FOUND'))
except:
    print('INVALID_JSON_RESPONSE')
")

echo "Token: $TOKEN"
export ADMIN_TOKEN="$TOKEN"
```

### **Step 3: Test Admin Endpoints**
```bash
# Test session stats
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  "http://localhost:8000/api/admin/session-stats"

# Expected: JSON with session statistics
# If 401: Check token and admin role
# If 404: Check if endpoint exists in api.py

# Test user search
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  "http://localhost:8000/api/admin/users/search?limit=5"

# Expected: JSON with users array
# If empty: Create test users first

# Test system analytics
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  "http://localhost:8000/api/admin/analytics/system"

# Expected: JSON with system metrics
```

---

## 🌐 **Frontend Testing**

### **Step 1: Serve Admin Files**
```bash
# Start simple HTTP server
cd /path/to/your/project
python -m http.server 8080

# Or use Node.js if available
npx serve . -p 8080
```

### **Step 2: Configure Admin Interface**
```javascript
// Edit admin_user_search.js
// Update the getAuthToken() method:
getAuthToken() {
    return 'YOUR_ACTUAL_TOKEN_HERE'; // Replace with token from Step 2
}

// Update API base URL if needed:
const API_BASE_URL = 'http://localhost:8000';
```

### **Step 3: Test Admin Dashboard**
```bash
# Open in browser
open http://localhost:8080/admin_dashboard.html

# Check browser console (F12) for errors
# Should see dashboard loading with metrics
```

**Browser Console Debugging:**
- **CORS errors**: Add CORS middleware to FastAPI
- **401 Unauthorized**: Check admin token
- **Network errors**: Verify API server is running
- **JavaScript errors**: Check file paths and syntax

---

## 🔧 **Common Issues & Solutions**

### **Database Issues**

#### **Migration Failed**
```bash
# Check current migration status
alembic current

# If no migration applied
alembic upgrade head

# If migration conflicts
alembic downgrade base
alembic upgrade head

# If table already exists error
# Drop tables manually and re-run migration
```

#### **Connection Issues**
```python
# Test database connection
from sqlalchemy import create_engine
import os

DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://user:pass@localhost/db")
engine = create_engine(DATABASE_URL)

try:
    with engine.connect() as conn:
        result = conn.execute("SELECT 1")
        print("✅ Database connection successful")
except Exception as e:
    print(f"❌ Database error: {e}")
    print("Check DATABASE_URL environment variable")
```

### **API Issues**

#### **Import Errors**
```bash
# Check if all files exist
ls -la *activity*.py *admin*.py session_tracking_middleware.py

# Test imports individually
python -c "from user_activity_logger import activity_logger; print('✅ Activity logger imported')"
python -c "from session_tracking_middleware import SessionTrackingMiddleware; print('✅ Middleware imported')"
python -c "from admin_data_service import admin_data_service; print('✅ Admin service imported')"
```

#### **Middleware Not Working**
```python
# Check if middleware is registered
from api import app
print("Registered middleware:")
for middleware in app.user_middleware:
    print(f"  {middleware.cls.__name__}")

# Should include SessionTrackingMiddleware
```

### **Frontend Issues**

#### **CORS Errors**
```python
# Add to api.py if not already present
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8080", "http://127.0.0.1:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

#### **Authentication Issues**
```javascript
// Test API call manually in browser console
fetch('http://localhost:8000/api/admin/session-stats', {
    headers: {
        'Authorization': 'Bearer YOUR_TOKEN_HERE'
    }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

---

## 📊 **Generate Test Data**

### **Quick Test Data Script**
```python
# Run this to create sample data for testing
from database import get_db, User, UserQuery, UserSession, UserInteraction
from datetime import datetime, timedelta
import random

db = next(get_db())

# Create test user if not exists
test_user = db.query(User).filter(User.username == "<EMAIL>").first()
if not test_user:
    test_user = User(
        username="<EMAIL>",
        hashed_password="dummy_hash",
        is_admin=False
    )
    db.add(test_user)
    db.commit()

# Create test queries
sample_queries = ["pizza debug", "øl test", "brød sample"]
for query_text in sample_queries:
    query = UserQuery(
        user_id=test_user.id,
        query_text=query_text,
        selected_stores=[1, 2],
        results_count=random.randint(1, 10),
        response_time_ms=random.randint(100, 500),
        was_successful=True,
        query_category="food_beverages"
    )
    db.add(query)

# Create test session
session = UserSession(
    user_id=test_user.id,
    session_id=f"debug_session_{random.randint(1000, 9999)}",
    session_start=datetime.utcnow() - timedelta(minutes=30),
    duration_seconds=1800,
    queries_count=3,
    interactions_count=5,
    device_type="desktop",
    browser="chrome"
)
db.add(session)

# Create test interaction
interaction = UserInteraction(
    user_id=test_user.id,
    session_id=session.session_id,
    interaction_type="product_clicked",
    target_type="product",
    target_id=123,
    interaction_data={"product_name": "Debug Pizza", "position": 1}
)
db.add(interaction)

db.commit()
print(f"✅ Created test data for user: {test_user.username}")
db.close()
```

---

## ✅ **Final Verification**

### **Complete System Test**
1. **Start FastAPI server** - No errors on startup
2. **Make a query** - Check database for logged query
3. **Check session** - Verify session created and tracked
4. **Test admin login** - Get valid JWT token
5. **Test admin API** - All endpoints return data
6. **Test admin UI** - Dashboard loads with charts
7. **Test user search** - Find test users and view profiles

### **Success Indicators**
- ✅ All database tables created
- ✅ Sessions automatically tracked
- ✅ Queries logged with full context
- ✅ Interactions captured
- ✅ Admin API returns data
- ✅ Admin interface displays charts
- ✅ No console errors in browser

**If all checks pass, your system is ready for production deployment!** 🚀
