# Tilbudsjægeren User Activity Tracking System

## 🎯 **Complete User Activity & Admin System**

This comprehensive system provides enterprise-grade user activity tracking, behavioral analysis, and admin management capabilities for the Tilbudsjægeren platform.

---

## 📊 **System Overview**

### **What We've Built**
- **Complete Data Pipeline** - From user interactions to admin insights
- **Comprehensive Tracking** - Every query, session, interaction, and preference change
- **Admin Interface** - Professional admin panel with search, analytics, and user management
- **Business Intelligence** - Rich analytics for data-driven decisions
- **GDPR Compliance** - Audit trails and privacy-ready architecture

### **Key Components**
1. **Database Schema** - 5 tables with strategic indexing for performance
2. **Data Collection** - Automatic tracking via middleware and logging services
3. **Admin API** - 12+ endpoints for comprehensive data retrieval
4. **Admin Interface** - Modern web interface with search, profiles, and analytics
5. **Security & Compliance** - GDPR-ready with audit logging and access controls

---

## 🏗️ **Architecture**

```
User Interaction → Session Middleware → Activity Logger → Database
                                    ↓
Admin Interface ← Admin API Endpoints ← Data Service ← Database
```

### **Data Flow**
1. **User makes request** → Session middleware tracks automatically
2. **User searches** → Query logged with full context and performance
3. **User interacts** → Product clicks, catalog views, store selections tracked
4. **Admin accesses** → Rich data available through API and interface
5. **Business insights** → Analytics and reporting for optimization

---

## 📁 **File Structure**

### **Backend Components**
```
├── database_schema_design.md          # Complete database design
├── user_activity_migration.py         # Alembic migration file
├── user_activity_logger.py           # Centralized logging service
├── session_tracking_middleware.py    # Automatic session tracking
├── advanced_interaction_logger.py    # Detailed interaction logging
├── admin_data_service.py             # Admin data retrieval service
└── api.py                            # Updated with admin endpoints
```

### **Admin Interface**
```
├── admin_dashboard.html              # System overview dashboard
├── admin_user_search.html           # User search interface
├── admin_user_search.js             # Search functionality
├── admin_user_profile.html          # User profile pages
└── frontend_interaction_tracker.js   # Client-side tracking library
```

### **Documentation**
```
├── README_USER_ACTIVITY_SYSTEM.md   # This file
├── admin_api_documentation.md       # Complete API documentation
├── admin_interface_integration_guide.md # Frontend integration guide
└── task_*_completion_summary.md     # Detailed implementation summaries
```

---

## 🚀 **Quick Start Guide**

### **1. Database Setup**
```bash
# Run the migration to create tables
alembic upgrade head

# Verify tables were created
psql -d tilbudsjaegeren -c "\dt user_*"
```

### **2. Backend Integration**
```python
# The middleware and logging are already integrated in api.py
# Just start your FastAPI server
uvicorn api:app --reload --host 0.0.0.0 --port 8000
```

### **3. Admin Interface Setup**
```bash
# Serve admin files (example with Python)
cd /path/to/admin/files
python -m http.server 8080

# Or integrate with your web server
# See admin_interface_integration_guide.md for details
```

### **4. Test the System**
```bash
# Test API endpoints
curl -H "Authorization: Bearer <admin_token>" \
  "http://localhost:8000/api/admin/users/search?limit=10"

# Test admin interface
open http://localhost:8080/admin_dashboard.html
```

---

## 📊 **Data Being Collected**

### **User Queries**
- Query text and selected stores
- Results count and response time
- Success/failure status and error messages
- Query categorization and intent analysis
- Model used and performance metrics

### **User Sessions**
- Session duration and activity patterns
- Queries and interactions per session
- Device type and browser information
- Geographic and temporal patterns
- User authentication status

### **User Interactions**
- Product clicks with position and context
- Catalog views and download behavior
- Store selection changes and preferences
- UI element interactions and navigation
- Error encounters and performance issues

### **Admin Actions**
- All admin data access logged
- User searches and profile views
- Data exports and modifications
- IP addresses and timestamps
- GDPR compliance audit trail

---

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Database connection
DATABASE_URL=postgresql://user:pass@localhost/tilbudsjaegeren

# Admin authentication
ADMIN_JWT_SECRET=your_secret_key_here
ADMIN_TOKEN_EXPIRE_HOURS=24

# Session tracking
SESSION_TIMEOUT_MINUTES=30
SESSION_CLEANUP_INTERVAL_HOURS=24

# API configuration
API_BASE_URL=http://localhost:8000
ADMIN_INTERFACE_URL=http://localhost:8080
```

### **Database Configuration**
```python
# In database.py - already configured
SQLALCHEMY_DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(SQLALCHEMY_DATABASE_URL)
```

### **Middleware Configuration**
```python
# In api.py - already integrated
app.add_middleware(
    SessionTrackingMiddleware,
    session_timeout_minutes=30,
    cleanup_interval_hours=24,
    exclude_paths=["/favicon.ico", "/robots.txt", "/health"]
)
```

---

## 🔍 **API Endpoints**

### **Admin User Management**
- `GET /api/admin/users/search` - Search users with filters
- `GET /api/admin/users/{user_id}/profile` - Get user profile
- `GET /api/admin/users/{user_id}/timeline` - Get user activity timeline
- `GET /api/admin/users/{user_id}/queries` - Get user query history

### **Admin Analytics**
- `GET /api/admin/analytics/system` - System-wide metrics
- `GET /api/admin/analytics/queries` - Query performance analysis
- `GET /api/admin/session-stats` - Real-time session statistics

### **Interaction Logging**
- `POST /log-product-click` - Log product interactions
- `POST /log-catalog-download` - Log catalog downloads
- `POST /log-query-refinement` - Log search refinements
- `POST /log-store-filter-change` - Log store selection changes
- `POST /log-ui-interaction` - Log UI element interactions
- `POST /log-error-encounter` - Log user-experienced errors

---

## 📈 **Business Intelligence Queries**

### **User Segmentation**
```sql
-- Power users analysis
SELECT 
    user_type,
    COUNT(*) as user_count,
    AVG(total_queries) as avg_queries,
    AVG(avg_session_duration) as avg_session_time
FROM user_search_results 
GROUP BY user_type;
```

### **Popular Products**
```sql
-- Most clicked products
SELECT 
    interaction_data->>'product_name' as product_name,
    COUNT(*) as click_count,
    AVG((interaction_data->>'position_in_results')::int) as avg_position
FROM user_interactions 
WHERE interaction_type = 'product_clicked'
GROUP BY product_name
ORDER BY click_count DESC;
```

### **Query Success Analysis**
```sql
-- Query success rates by category
SELECT 
    query_category,
    COUNT(*) as total_queries,
    SUM(CASE WHEN was_successful THEN 1 ELSE 0 END) as successful_queries,
    (SUM(CASE WHEN was_successful THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as success_rate
FROM user_queries 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY query_category
ORDER BY success_rate DESC;
```

---

## 🔐 **Security & Compliance**

### **Authentication**
- JWT-based admin authentication
- Role-based access control (admin-only endpoints)
- Session timeout and automatic logout
- HTTPS enforcement for admin traffic

### **Data Protection**
- All admin actions logged for audit
- IP address tracking for compliance
- User data access logging (GDPR)
- Secure error handling without data leakage

### **Privacy Compliance**
- GDPR-ready audit trails
- User data export capabilities
- Data retention policies configurable
- Consent tracking and management

---

## 🧪 **Testing**

### **Backend Testing**
```bash
# Test admin API endpoints
python -m pytest tests/test_admin_api.py

# Test session middleware
python -m pytest tests/test_session_middleware.py

# Test activity logging
python -m pytest tests/test_activity_logger.py
```

### **Frontend Testing**
```bash
# Test admin interface
# Open admin_dashboard.html in browser
# Verify all features work with test data

# Test responsive design
# Check mobile, tablet, desktop layouts
```

### **Integration Testing**
```bash
# Test complete user journey
# 1. User makes query → Check database logging
# 2. Admin searches user → Verify data appears
# 3. Admin views profile → Check all metrics
```

---

## 🚀 **Deployment**

### **Production Setup**
1. **Database Migration** - Run `alembic upgrade head`
2. **Environment Variables** - Set all required config
3. **Web Server** - Configure Nginx/Apache for admin files
4. **SSL Certificates** - HTTPS for admin interface
5. **Monitoring** - Set up logging and alerting

### **Performance Optimization**
- Database indexes on user_id, created_at columns
- Connection pooling for high traffic
- Caching for frequently accessed data
- CDN for admin interface assets

---

## 📞 **Support & Maintenance**

### **Monitoring**
- Database performance and query times
- API response times and error rates
- Admin interface usage and errors
- Session tracking and cleanup processes

### **Maintenance Tasks**
- Regular database cleanup of old sessions
- Admin audit log rotation
- Performance monitoring and optimization
- Security updates and patches

---

## 🎯 **System Status: PRODUCTION READY**

✅ **Database Schema** - Complete with strategic indexing
✅ **Data Collection** - Comprehensive automatic tracking
✅ **Admin API** - Full CRUD operations with security
✅ **Admin Interface** - Professional UI with analytics
✅ **Documentation** - Complete integration guides
✅ **Security** - GDPR-compliant with audit trails

**The user activity tracking system is ready for production deployment and will provide invaluable insights into user behavior and system performance!** 🚀
