<!DOCTYPE html>
<html lang="da">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tilbudsjægeren - Mobile Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Native app feel styles */
        body {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: none;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .touch-active:active {
            transform: scale(0.98);
            opacity: 0.8;
        }
        
        .tab-transition {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div id="app" class="min-h-screen flex flex-col">
        <!-- Hamburger Menu Button -->
        <button id="menuBtn" class="fixed top-4 left-4 z-50 p-2 bg-white/80 backdrop-blur-sm text-gray-700 rounded-lg shadow-md hover:bg-white hover:shadow-lg transition-all duration-200">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- Hamburger Menu Overlay -->
        <div id="menuOverlay" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 hidden transition-opacity duration-300"></div>

        <!-- Hamburger Menu Panel -->
        <div id="menuPanel" class="fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50 transform -translate-x-full transition-transform duration-300 ease-out">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">T</span>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Tilbudsjægeren</h2>
                        <p class="text-sm text-gray-600">Find de bedste tilbud</p>
                    </div>
                </div>
            </div>
            <div class="py-4">
                <div class="px-6 py-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors cursor-pointer">
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">ℹ️</div>
                    <div>
                        <div class="text-sm font-medium text-gray-900">Om Tilbudsjægeren</div>
                        <div class="text-xs text-gray-500">Læs om vores mission</div>
                    </div>
                </div>
                <div class="px-6 py-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors cursor-pointer">
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">👤</div>
                    <div>
                        <div class="text-sm font-medium text-gray-900">Min Profil</div>
                        <div class="text-xs text-gray-500">Indstillinger og præferencer</div>
                    </div>
                </div>
                <div class="px-6 py-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors cursor-pointer">
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">📊</div>
                    <div>
                        <div class="text-sm font-medium text-gray-900">Mine Data</div>
                        <div class="text-xs text-gray-500">Se dine søgninger og aktivitet</div>
                    </div>
                </div>
                <div class="px-6 py-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors cursor-pointer">
                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">🛡️</div>
                    <div>
                        <div class="text-sm font-medium text-gray-900">Privatliv</div>
                        <div class="text-xs text-gray-500">Privatlivspolitik og sikkerhed</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <div class="bg-white px-4 pt-16 pb-4 shadow-sm">
            <div class="text-center">
                <h1 class="text-2xl font-bold text-blue-600">🦉 Tilbudsjægeren</h1>
                <p class="text-sm text-gray-600">Mobile-First Demo</p>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 pb-20 overflow-y-auto">
            <!-- Search Tab Content -->
            <div id="searchTab" class="p-6 space-y-6">
                <div class="text-center">
                    <div class="w-20 h-20 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <span class="text-2xl">🔍</span>
                    </div>
                    <h2 class="text-xl font-medium text-gray-800 mb-2">Find de bedste tilbud</h2>
                    <p class="text-gray-600 text-sm">Skriv hvad du søger, og vi finder de billigste priser</p>
                </div>
                
                <div class="relative">
                    <input type="text" placeholder="Søg efter produkter... (f.eks. 'økologisk mælk')" 
                           class="w-full px-6 py-4 text-lg border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <button class="absolute right-2 top-2 px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors">
                        Søg
                    </button>
                </div>

                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Populære søgninger</h3>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-2 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200 hover:bg-blue-100 transition-colors touch-active">økologisk mælk</button>
                        <button class="px-3 py-2 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200 hover:bg-blue-100 transition-colors touch-active">billigt brød</button>
                        <button class="px-3 py-2 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200 hover:bg-blue-100 transition-colors touch-active">kaffe tilbud</button>
                        <button class="px-3 py-2 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200 hover:bg-blue-100 transition-colors touch-active">friske æg</button>
                    </div>
                </div>
            </div>

            <!-- Shopping Tab Content -->
            <div id="shoppingTab" class="p-6 text-center hidden">
                <div class="w-16 h-16 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span class="text-2xl">🛒</span>
                </div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">Indkøbsliste</h2>
                <p class="text-gray-600 mb-6">Produkter du tilføjer fra søgeresultater vil vises her</p>
                <button class="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors touch-active">
                    Start med at søge
                </button>
            </div>

            <!-- Favorites Tab Content -->
            <div id="favoritesTab" class="p-6 text-center hidden">
                <div class="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span class="text-2xl">❤️</span>
                </div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">Favoritter</h2>
                <p class="text-gray-600 mb-6">Tryk på hjertet på produkter for at tilføje dem til dine favoritter</p>
                <button class="bg-red-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors touch-active">
                    Find produkter
                </button>
            </div>

            <!-- Profile Tab Content -->
            <div id="profileTab" class="p-6 hidden">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <span class="text-2xl">👤</span>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900">Jonas</h2>
                    <p class="text-gray-600"><EMAIL></p>
                </div>
                
                <div class="space-y-3">
                    <button class="w-full bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors text-left touch-active">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">👤</div>
                        <div class="flex-1">
                            <div class="font-medium text-gray-900">Rediger profil</div>
                            <div class="text-sm text-gray-500">Navn, email og præferencer</div>
                        </div>
                        <span class="text-gray-400">›</span>
                    </button>
                    <button class="w-full bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors text-left touch-active">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">⚙️</div>
                        <div class="flex-1">
                            <div class="font-medium text-gray-900">Indstillinger</div>
                            <div class="text-sm text-gray-500">App indstillinger</div>
                        </div>
                        <span class="text-gray-400">›</span>
                    </button>
                </div>
            </div>
        </main>

        <!-- Bottom Tab Navigation -->
        <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
            <div class="flex items-center justify-around px-2 py-1">
                <button class="tab-btn flex flex-col items-center justify-center px-3 py-2 min-w-0 flex-1 tab-transition text-blue-600 scale-105" data-tab="search">
                    <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span class="text-xs font-medium">Søg</span>
                    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full"></div>
                </button>
                
                <button class="tab-btn flex flex-col items-center justify-center px-3 py-2 min-w-0 flex-1 tab-transition text-gray-500 hover:text-gray-700" data-tab="shopping">
                    <div class="relative">
                        <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8"/>
                        </svg>
                        <div class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">3</div>
                    </div>
                    <span class="text-xs font-medium">Indkøb</span>
                </button>
                
                <button class="tab-btn flex flex-col items-center justify-center px-3 py-2 min-w-0 flex-1 tab-transition text-gray-500 hover:text-gray-700" data-tab="favorites">
                    <div class="relative">
                        <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                        <div class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">2</div>
                    </div>
                    <span class="text-xs font-medium">Favoritter</span>
                </button>
                
                <button class="tab-btn flex flex-col items-center justify-center px-3 py-2 min-w-0 flex-1 tab-transition text-gray-500 hover:text-gray-700" data-tab="profile">
                    <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    <span class="text-xs font-medium">Profil</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabs = {
            search: document.getElementById('searchTab'),
            shopping: document.getElementById('shoppingTab'),
            favorites: document.getElementById('favoritesTab'),
            profile: document.getElementById('profileTab')
        };

        function switchTab(activeTab) {
            // Hide all tabs
            Object.values(tabs).forEach(tab => tab.classList.add('hidden'));
            
            // Show active tab
            tabs[activeTab].classList.remove('hidden');
            
            // Update button states
            tabBtns.forEach(btn => {
                if (btn.dataset.tab === activeTab) {
                    btn.classList.add('text-blue-600', 'scale-105');
                    btn.classList.remove('text-gray-500');
                    btn.innerHTML = btn.innerHTML.replace('</span>', '</span><div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full"></div>');
                } else {
                    btn.classList.remove('text-blue-600', 'scale-105');
                    btn.classList.add('text-gray-500');
                    btn.innerHTML = btn.innerHTML.replace('<div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full"></div>', '');
                }
            });
        }

        // Add click listeners to tab buttons
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                switchTab(btn.dataset.tab);
            });
        });

        // Hamburger menu functionality
        const menuBtn = document.getElementById('menuBtn');
        const menuOverlay = document.getElementById('menuOverlay');
        const menuPanel = document.getElementById('menuPanel');

        function toggleMenu() {
            const isOpen = !menuPanel.classList.contains('-translate-x-full');
            
            if (isOpen) {
                // Close menu
                menuPanel.classList.add('-translate-x-full');
                menuOverlay.classList.add('hidden');
                document.body.style.overflow = 'unset';
            } else {
                // Open menu
                menuPanel.classList.remove('-translate-x-full');
                menuOverlay.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        menuBtn.addEventListener('click', toggleMenu);
        menuOverlay.addEventListener('click', toggleMenu);

        // Close menu on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                toggleMenu();
            }
        });
    </script>
</body>
</html>
