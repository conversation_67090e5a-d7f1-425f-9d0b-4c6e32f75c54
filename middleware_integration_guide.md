# Session Tracking Middleware Integration Guide

## ✅ Task 6 Complete: Session Tracking Middleware Created

### **What We've Built:**

#### **📊 Comprehensive Session Middleware**
- **Automatic session tracking** for all requests
- **Session lifecycle management** (create, update, cleanup)
- **Cookie-based session persistence** for anonymous users
- **Database integration** with user activity logging
- **Performance optimized** with in-memory session cache

#### **🎯 Key Features**
- **Zero-configuration tracking** - Works automatically once added
- **Path exclusion** - Skip tracking for static files, health checks
- **User identification** - Handles both authenticated and anonymous users
- **Browser/device detection** - Automatic classification from user agents
- **Graceful error handling** - Never breaks the main application
- **Periodic cleanup** - Automatic removal of old sessions

## **Integration Steps**

### **Step 1: Add Middleware to FastAPI App**

Add this to your `api.py` file after creating the FastAPI app instance:

```python
from session_tracking_middleware import SessionTrackingMiddleware

# Create FastAPI app
app = FastAPI(title="Tilbudsjægeren API", version="1.0.0")

# Add session tracking middleware
app.add_middleware(
    SessionTrackingMiddleware,
    session_timeout_minutes=30,  # Session timeout
    cleanup_interval_hours=24,   # Cleanup frequency
    exclude_paths=[              # Paths to exclude from tracking
        "/favicon.ico",
        "/robots.txt", 
        "/health",
        "/static/",
        "/docs",
        "/redoc",
        "/openapi.json"
    ]
)

# Your existing middleware and routes...
```

### **Step 2: Update Existing Endpoints (Optional)**

Since the middleware handles session tracking automatically, you can simplify your existing endpoints by removing manual session tracking:

#### **Before (Manual Session Tracking):**
```python
@app.post("/ask")
async def ask_query(
    request_obj: AskRequest,
    request: Request,
    response: Response,  # No longer needed
    db: Session = Depends(get_db),
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    # Manual session tracking (can be removed)
    session_id = track_page_view(request, response, 
                                user_id=current_user.id if current_user else None)
    
    return await ask_query_internal(request_obj, db, current_user, session_id)
```

#### **After (Automatic Session Tracking):**
```python
from session_tracking_middleware import get_session_id_from_request

@app.post("/ask")
async def ask_query(
    request_obj: AskRequest,
    request: Request,  # Response parameter no longer needed
    db: Session = Depends(get_db),
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    # Get session ID from middleware
    session_id = get_session_id_from_request(request)
    
    return await ask_query_internal(request_obj, db, current_user, session_id)
```

### **Step 3: Add Session Monitoring Endpoint (Optional)**

Add an endpoint to monitor session statistics:

```python
@app.get("/api/admin/session-stats")
async def get_session_stats(admin_user: schemas.User = Depends(auth.require_admin)):
    """Get current session statistics for monitoring"""
    # Access the middleware instance
    for middleware in app.user_middleware:
        if isinstance(middleware.cls, SessionTrackingMiddleware):
            return middleware.cls.get_session_stats()
    
    return {"error": "Session tracking middleware not found"}
```

## **Middleware Configuration Options**

### **SessionTrackingMiddleware Parameters**

```python
SessionTrackingMiddleware(
    session_timeout_minutes=30,     # How long sessions stay active
    cleanup_interval_hours=24,      # How often to cleanup old sessions
    exclude_paths=[                 # Paths to skip tracking
        "/favicon.ico",
        "/robots.txt",
        "/health",
        "/metrics", 
        "/static/",
        "/docs",
        "/redoc",
        "/openapi.json"
    ]
)
```

### **Customization Options**

#### **Custom Path Exclusions**
```python
# Exclude additional paths
exclude_paths = [
    "/favicon.ico",
    "/robots.txt",
    "/health",
    "/api/internal/",  # Internal API calls
    "/webhooks/",      # Webhook endpoints
    "/admin/static/"   # Admin static files
]
```

#### **Custom Session Timeout**
```python
# Shorter timeout for high-traffic sites
session_timeout_minutes=15

# Longer timeout for better user experience
session_timeout_minutes=60
```

## **How the Middleware Works**

### **Request Processing Flow**
1. **Request arrives** → Check if path should be excluded
2. **Get/create session** → Extract session ID from cookie or create new
3. **Track request** → Log page view, update session data
4. **Process request** → Call the actual endpoint
5. **Update activity** → Record query/interaction counts
6. **Set cookie** → Ensure session cookie is set on response
7. **Cleanup** → Periodically remove old sessions

### **Session Data Structure**
```python
session_data = {
    'created_at': datetime.utcnow(),
    'last_activity': datetime.utcnow(),
    'user_id': None,  # Set when user authenticates
    'queries_count': 0,
    'interactions_count': 0,
    'pages_viewed': 0,
    'device_type': 'desktop',  # mobile, tablet, desktop
    'browser': 'chrome',       # chrome, firefox, safari, etc.
    'ip_address': '***********',
    'user_agent': 'Mozilla/5.0...',
    'started_in_db': True      # Whether session exists in database
}
```

### **Database Integration**
- **Session start** → Creates UserSession record in database
- **Session activity** → Updates query/interaction counts
- **Session end** → Calculates duration and final metrics
- **Cleanup** → Removes old sessions from memory and database

## **Benefits of Middleware Approach**

### **Automatic Tracking**
- ✅ **Zero configuration** - Works on all endpoints automatically
- ✅ **Consistent data** - Same tracking logic everywhere
- ✅ **No code duplication** - Single implementation for all endpoints
- ✅ **Easy maintenance** - Update tracking logic in one place

### **Performance Optimized**
- ✅ **In-memory cache** - Fast session lookups
- ✅ **Batch database operations** - Efficient session management
- ✅ **Automatic cleanup** - Prevents memory leaks
- ✅ **Path exclusions** - Skip unnecessary tracking

### **Error Resilience**
- ✅ **Graceful degradation** - Never breaks the main application
- ✅ **Exception handling** - Logs errors but continues processing
- ✅ **Fallback behavior** - Works even if database is unavailable
- ✅ **Optional tracking** - Can be disabled without code changes

## **Monitoring and Debugging**

### **Session Statistics**
```python
# Get current session stats
stats = middleware.get_session_stats()
print(f"Active sessions: {stats['active_sessions']}")
print(f"Total queries: {stats['total_queries']}")
print(f"Total interactions: {stats['total_interactions']}")
```

### **Manual Session Access**
```python
from session_tracking_middleware import get_session_id_from_request, get_session_data

@app.get("/debug/my-session")
async def debug_session(request: Request):
    session_id = get_session_id_from_request(request)
    session_data = get_session_data(session_id, middleware_instance)
    return {
        "session_id": session_id,
        "session_data": session_data
    }
```

### **Logging Configuration**
```python
import logging

# Enable debug logging for session tracking
logging.getLogger("session_tracking_middleware").setLevel(logging.DEBUG)
```

## **Testing the Middleware**

### **Basic Functionality Test**
1. **Start your FastAPI app** with the middleware added
2. **Make a request** to any endpoint
3. **Check browser cookies** - Should see `tilbudsjaegeren_session` cookie
4. **Check database** - Should see new UserSession record
5. **Make more requests** - Session should be reused

### **Session Persistence Test**
1. **Make a request** and note the session ID
2. **Close browser tab** (but keep browser open)
3. **Open new tab** and make another request
4. **Check session ID** - Should be the same (cookie persisted)

### **Cleanup Test**
1. **Wait for cleanup interval** (or reduce it for testing)
2. **Check logs** - Should see cleanup messages
3. **Check database** - Old sessions should be ended
4. **Check memory** - Old sessions removed from middleware cache

## **Migration from Manual Tracking**

### **Phase 1: Add Middleware**
- Add middleware to FastAPI app
- Keep existing manual tracking code
- Verify both systems work together

### **Phase 2: Simplify Endpoints**
- Remove manual `track_page_view` calls
- Remove `Response` parameters where not needed
- Use `get_session_id_from_request` helper

### **Phase 3: Clean Up**
- Remove unused imports
- Remove manual session management code
- Update tests to work with middleware

## **Middleware Status: ✅ READY FOR INTEGRATION**

The session tracking middleware is complete and ready to be added to your FastAPI application. It will automatically handle session tracking for all requests with zero configuration required.

**Next: Add middleware to api.py and test the integration!** 🚀
