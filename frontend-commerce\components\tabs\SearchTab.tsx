"use client";

import { useState, useEffect } from 'react';
import Image from "next/image";
import Hero from '@/components/landing/Hero';
import QuerySection from '@/components/landing/QuerySection';
import CatalogList from '@/components/landing/CatalogList';

export default function SearchTab() {
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [popularSearches] = useState([
    'økologisk mælk',
    'billigt brød', 
    'kaffe tilbud',
    'friske æg',
    'discount kød'
  ]);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (e) {
        console.error('Failed to load recent searches:', e);
      }
    }
  }, []);

  const addRecentSearch = (query: string) => {
    const updated = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Clean centered logo and search - Google-like design */}
      <div className="flex-1 flex flex-col justify-center px-8 pb-32">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="w-24 h-24 relative mx-auto mb-6">
            <Image
              src="/logos/logo-samlet.png"
              alt="Tilbudsjægeren"
              fill
              className="object-contain"
              priority
            />
          </div>
        </div>

        {/* Helpful but minimal guidance */}
        <div className="text-center mb-8">
          <h1 className="text-xl font-medium text-gray-800 mb-2">Find de bedste tilbud</h1>
          <p className="text-gray-600">Skriv hvad du søger, og vi finder de billigste priser</p>
        </div>

        {/* Clean search with subtle help */}
        <div className="max-w-2xl mx-auto w-full mb-6">
          <QuerySection onSearch={addRecentSearch} />
        </div>

        {/* Gentle store selection */}
        <div className="text-center max-w-2xl mx-auto w-full">
          <p className="text-sm text-gray-600 mb-4">Vælg dine foretrukne butikker (valgfrit)</p>
          <div className="flex justify-center space-x-3 mb-6">
            <div className="w-14 h-14 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-gray-300 cursor-pointer transition-all">
              <div className="w-full h-full bg-red-500 flex items-center justify-center text-white font-bold text-xs">NETTO</div>
            </div>
            <div className="w-14 h-14 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-gray-300 cursor-pointer transition-all">
              <div className="w-full h-full bg-blue-600 flex items-center justify-center text-white font-bold text-xs">LIDL</div>
            </div>
            <div className="w-14 h-14 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-gray-300 cursor-pointer transition-all">
              <div className="w-full h-full bg-green-600 flex items-center justify-center text-white font-bold text-xs">REMA</div>
            </div>
            <div className="w-14 h-14 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-gray-300 cursor-pointer transition-all">
              <div className="w-full h-full bg-orange-500 flex items-center justify-center text-white font-bold text-xs">FØTEX</div>
            </div>
          </div>
        </div>

        {/* Recent Searches - Only show if they exist */}
        {recentSearches.length > 0 && (
          <div className="max-w-2xl mx-auto w-full mt-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3 text-center">Seneste søgninger</h3>
            <div className="flex flex-wrap gap-2 justify-center">
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => {
                    // TODO: Trigger search with this query
                    console.log('Search for:', search);
                  }}
                  className="px-3 py-2 bg-gray-50 text-gray-700 rounded-full text-sm border border-gray-200 hover:border-blue-300 hover:text-blue-600 transition-colors"
                >
                  {search}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
