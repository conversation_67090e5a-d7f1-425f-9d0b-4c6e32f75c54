"use client";

import { useState, useEffect } from 'react';
import Image from "next/image";
import Hero from '@/components/landing/Hero';
import QuerySection from '@/components/landing/QuerySection';
import CatalogList from '@/components/landing/CatalogList';

export default function SearchTab() {
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [popularSearches] = useState([
    'økologisk mælk',
    'billigt brød', 
    'kaffe tilbud',
    'friske æg',
    'discount kød'
  ]);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (e) {
        console.error('Failed to load recent searches:', e);
      }
    }
  }, []);

  const addRecentSearch = (query: string) => {
    const updated = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with logo - Clean Guidance style */}
      <div className="bg-white px-4 pt-16 pb-8 shadow-sm">
        <div className="max-w-2xl mx-auto text-center">
          {/* Logo */}
          <div className="w-20 h-20 relative mx-auto mb-4">
            <Image 
              src="/logos/logo-samlet.png" 
              alt="Tilbudsjægeren"
              fill
              className="object-contain"
            />
          </div>
          
          {/* Clean guidance text */}
          <h1 className="text-xl font-medium text-gray-800 mb-2">Find de bedste tilbud</h1>
          <p className="text-gray-600 text-sm">Skriv hvad du søger, og vi finder de billigste priser</p>
        </div>
      </div>

      {/* Main content */}
      <div className="px-4 py-6 space-y-8">
        {/* Search Section */}
        <div className="max-w-2xl mx-auto">
          <QuerySection onSearch={addRecentSearch} />
        </div>

        {/* Recent Searches */}
        {recentSearches.length > 0 && (
          <div className="max-w-2xl mx-auto">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Seneste søgninger</h3>
            <div className="flex flex-wrap gap-2">
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => {
                    // TODO: Trigger search with this query
                    console.log('Search for:', search);
                  }}
                  className="px-3 py-2 bg-white text-gray-700 rounded-full text-sm border border-gray-200 hover:border-blue-300 hover:text-blue-600 transition-colors"
                >
                  {search}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Popular Searches */}
        <div className="max-w-2xl mx-auto">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Populære søgninger</h3>
          <div className="flex flex-wrap gap-2">
            {popularSearches.map((search, index) => (
              <button
                key={index}
                onClick={() => {
                  // TODO: Trigger search with this query
                  console.log('Search for:', search);
                  addRecentSearch(search);
                }}
                className="px-3 py-2 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200 hover:bg-blue-100 transition-colors"
              >
                {search}
              </button>
            ))}
          </div>
        </div>

        {/* Catalog List */}
        <div className="max-w-5xl mx-auto">
          <CatalogList />
        </div>
      </div>
    </div>
  );
}
