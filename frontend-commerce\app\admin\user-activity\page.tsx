"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';

interface SystemStats {
  users: {
    total: number;
    active: number;
    new_this_month: number;
  };
  queries: {
    total: number;
    successful: number;
    success_rate: number;
  };
  sessions: {
    total: number;
    avg_duration: number;
  };
  interactions: {
    total: number;
  };
}

interface SessionStats {
  total_sessions: number;
  active_sessions: number;
  total_queries: number;
  total_interactions: number;
  last_updated: string;
}

export default function UserActivityDashboard() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [sessionStats, setSessionStats] = useState<SessionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect non-admin users
  useEffect(() => {
    if (!isLoading && (!user || !user.is_admin)) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      if (!token) {
        throw new Error('No authentication token found');
      }

      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      // Fetch system analytics
      const systemResponse = await fetch('http://localhost:6969/api/admin/analytics/system', { headers });
      if (!systemResponse.ok) {
        throw new Error(`System analytics failed: ${systemResponse.status}`);
      }
      const systemData = await systemResponse.json();
      setSystemStats(systemData);

      // Fetch session stats
      const sessionResponse = await fetch('http://localhost:6969/api/admin/session-stats', { headers });
      if (!sessionResponse.ok) {
        throw new Error(`Session stats failed: ${sessionResponse.status}`);
      }
      const sessionData = await sessionResponse.json();
      setSessionStats(sessionData);

      setError(null);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.is_admin) {
      fetchData();
      
      // Auto-refresh every 30 seconds
      const interval = setInterval(fetchData, 30000);
      return () => clearInterval(interval);
    }
  }, [user]);

  if (isLoading || !user || !user.is_admin) {
    return <div className="flex justify-center items-center h-64">Indlæser...</div>;
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-10 px-4">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <h3 className="text-lg font-medium text-red-800">Fejl ved indlæsning af data</h3>
          <p className="text-red-600 mt-2">{error}</p>
          <button 
            onClick={fetchData}
            className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Prøv igen
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-10 px-4 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          📊 User Activity Dashboard
        </h1>
        <button 
          onClick={fetchData}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Opdaterer...' : 'Opdater Data'}
        </button>
      </div>

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Aktive Brugere</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {systemStats?.users?.active || sessionStats?.active_sessions || 0}
              </p>
              <p className="text-xs text-gray-500">
                af {systemStats?.users?.total || 0} total
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Søgninger</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {systemStats?.queries?.total || sessionStats?.total_queries || 0}
              </p>
              <p className="text-xs text-gray-500">
                {systemStats?.queries?.success_rate?.toFixed(1) || '100.0'}% succes
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Sessioner</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {systemStats?.sessions?.total || sessionStats?.total_sessions || 0}
              </p>
              <p className="text-xs text-gray-500">
                Ø {Math.round(systemStats?.sessions?.avg_duration || 0)} min
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Interaktioner</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {systemStats?.interactions?.total || sessionStats?.total_interactions || 0}
              </p>
              <p className="text-xs text-gray-500">Brugerengagement</p>
            </div>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">System Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">API Status</span>
            <span className="text-green-600 font-medium">Operationel</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Database</span>
            <span className="text-green-600 font-medium">Forbundet</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Sidste opdatering</span>
            <span className="text-gray-600 dark:text-gray-400">
              {sessionStats?.last_updated ? new Date(sessionStats.last_updated).toLocaleTimeString('da-DK') : 'Nu'}
            </span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Hurtige Handlinger</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            onClick={() => router.push('/admin/users')}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            👥 Se Brugere
          </button>
          <button 
            onClick={fetchData}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            🔄 Opdater Data
          </button>
          <button 
            onClick={() => window.open('http://localhost:6969/docs', '_blank')}
            className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
          >
            📚 API Docs
          </button>
        </div>
      </div>
    </div>
  );
}
