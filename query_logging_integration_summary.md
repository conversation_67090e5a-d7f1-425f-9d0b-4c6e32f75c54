# Query Logging Integration Summary

## ✅ Task 5 Complete: Query Logging Integrated into API Endpoints

### **What We've Accomplished:**

#### **📊 Complete Query Logging Integration**
- **POST /ask endpoint** - Full query logging with performance metrics
- **GET /query endpoint** - Legacy endpoint with session tracking
- **Session management** - Automatic session tracking for all requests
- **Error handling** - Failed queries logged with error details
- **Response enrichment** - Query IDs returned to frontend

#### **🎯 Interaction Logging Added**
- **Catalog views** - `/catalogs/{catalog_id}` endpoint logs catalog interactions
- **Store list access** - `/stores` endpoint logs page views
- **Store selection** - New `/log-store-selection` endpoint for user preferences
- **Source query tracking** - Links interactions back to originating queries

#### **⚡ Performance Monitoring**
- **Response time tracking** - Every query timed in milliseconds
- **Success/failure rates** - Comprehensive error logging
- **Results counting** - Track how many products returned
- **Model performance** - Track which AI models perform best

## **Integration Details**

### **Query Endpoint Enhancements**

#### **POST /ask Endpoint**
```python
@app.post("/ask")
async def ask_query(
    request_obj: AskRequest,
    request: Request,
    response: Response,  # Added for session management
    db: Session = Depends(get_db),
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    # Track session and get session ID
    session_id = track_page_view(request, response, 
                                user_id=current_user.id if current_user else None)
    
    return await ask_query_internal(request_obj, db, current_user, session_id)
```

#### **Enhanced Internal Logic**
```python
async def ask_query_internal(request: AskRequest, db: Session, current_user: Optional[schemas.User], session_id: Optional[str] = None):
    start_time = time.time()
    
    try:
        # Process query...
        query_result = process_natural_language_query(...)
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Log successful query
        query_id = log_query_activity(
            query_text=request.query,
            results_count=len(products),
            response_time_ms=response_time_ms,
            was_successful=True,
            user_id=current_user.id if current_user else None,
            session_id=session_id,
            selected_stores=request.selected_catalog_ids,
            query_model=request.query_model or 'gemini-2.5-flash-lite'
        )
        
        # Increment session query count
        increment_session_query_count(session_id)
        
        return {
            "response": query_result.get('answer'),
            "products": products,
            "query_id": query_id  # New: Return query ID to frontend
        }
        
    except Exception as e:
        # Log failed query
        response_time_ms = int((time.time() - start_time) * 1000)
        log_query_activity(
            query_text=request.query,
            results_count=0,
            response_time_ms=response_time_ms,
            was_successful=False,
            error_message=str(e),
            user_id=current_user.id if current_user else None,
            session_id=session_id,
            selected_stores=request.selected_catalog_ids,
            query_model=request.query_model or 'gemini-2.5-flash-lite'
        )
        raise e
```

### **Interaction Logging Enhancements**

#### **Catalog View Tracking**
```python
@app.get("/catalogs/{catalog_id}")
async def get_catalog_by_id(
    catalog_id: int, 
    request: Request,
    response: Response,  # Added for session tracking
    db: Session = Depends(get_db),
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    # Track session
    session_id = track_page_view(request, response, 
                                user_id=current_user.id if current_user else None)
    
    # Get source query ID from query parameters
    source_query_id = request.query_params.get("query_id")
    
    # Log catalog view interaction
    log_user_interaction(
        interaction_type="catalog_viewed",
        user_id=current_user.id if current_user else None,
        session_id=session_id,
        target_type="catalog",
        target_id=catalog_id,
        source_query_id=int(source_query_id) if source_query_id else None,
        interaction_data={
            "catalog_title": catalog.title,
            "store_id": catalog.store_id
        }
    )
```

#### **Store Selection Tracking**
```python
@app.post("/log-store-selection")
async def log_store_selection(
    request: Request,
    response: Response,
    store_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Log when user selects/deselects stores for search"""
    session_id = track_page_view(request, response, 
                                user_id=current_user.id if current_user else None)
    
    selected_stores = store_data.get("selected_stores", [])
    
    # Log store selection interaction
    log_user_interaction(
        interaction_type="store_selected",
        user_id=current_user.id if current_user else None,
        session_id=session_id,
        interaction_data={
            "selected_stores": selected_stores,
            "store_count": len(selected_stores)
        }
    )
    
    return {"status": "logged", "selected_stores": selected_stores}
```

## **Data Being Collected**

### **Query Data**
- ✅ **Query text** - Exact user input
- ✅ **Selected stores** - Which catalogs they searched
- ✅ **Results count** - How many products found
- ✅ **Response time** - Performance in milliseconds
- ✅ **Success/failure** - Whether query worked
- ✅ **Error messages** - What went wrong (if anything)
- ✅ **AI model used** - Which Gemini model processed the query
- ✅ **Auto-categorization** - Food, household, electronics, etc.
- ✅ **Query intent** - Price comparison, specific product, etc.

### **Session Data**
- ✅ **Session ID** - Unique identifier for user session
- ✅ **User ID** - If authenticated (null for anonymous)
- ✅ **Device type** - Mobile, desktop, tablet
- ✅ **Browser** - Chrome, Firefox, Safari, etc.
- ✅ **IP address** - Geographic insights
- ✅ **Session duration** - How long they stay
- ✅ **Query count** - How many searches per session
- ✅ **Page views** - Navigation patterns

### **Interaction Data**
- ✅ **Catalog views** - Which PDFs they open
- ✅ **Store selections** - Preference changes
- ✅ **Page navigation** - How they move through the site
- ✅ **Source query tracking** - Which query led to each interaction
- ✅ **Interaction context** - Additional metadata per interaction

## **Frontend Integration Required**

### **Query ID Usage**
The frontend now receives a `query_id` in query responses:
```javascript
// Frontend can now track which query led to interactions
const response = await fetch('/ask', {
    method: 'POST',
    body: JSON.stringify({query: "pizza", selected_catalog_ids: [1,2,3]})
});

const data = await response.json();
const queryId = data.query_id; // Use this for interaction tracking

// When user clicks on catalog, include query_id
window.open(`/catalogs/123?query_id=${queryId}`);
```

### **Store Selection Logging**
```javascript
// Log when user changes store selection
function onStoreSelectionChange(selectedStores) {
    fetch('/log-store-selection', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({selected_stores: selectedStores})
    });
}
```

## **Admin Deep-Dive Capabilities Enabled**

### **User Query Analysis**
```sql
-- Get user's complete query history
SELECT created_at, query_text, selected_stores, results_count, response_time_ms, was_successful
FROM user_queries 
WHERE user_id = 123 
ORDER BY created_at DESC;

-- User's search patterns
SELECT query_category, COUNT(*) as count
FROM user_queries 
WHERE user_id = 123 
GROUP BY query_category 
ORDER BY count DESC;
```

### **Performance Analytics**
```sql
-- Average response times by model
SELECT query_model, AVG(response_time_ms) as avg_response_time, COUNT(*) as query_count
FROM user_queries 
WHERE was_successful = true
GROUP BY query_model;

-- Success rates by category
SELECT query_category, 
       COUNT(*) as total_queries,
       SUM(CASE WHEN was_successful THEN 1 ELSE 0 END) as successful_queries,
       (SUM(CASE WHEN was_successful THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as success_rate
FROM user_queries 
GROUP BY query_category;
```

### **Business Intelligence**
```sql
-- Most popular queries
SELECT query_text, COUNT(*) as frequency
FROM user_queries 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY query_text 
ORDER BY frequency DESC 
LIMIT 20;

-- Store popularity
SELECT selected_stores, COUNT(*) as usage_count
FROM user_queries 
WHERE selected_stores IS NOT NULL
GROUP BY selected_stores 
ORDER BY usage_count DESC;
```

## **Next Steps**

### **Task 6: Add Session Tracking Middleware**
- Create FastAPI middleware for automatic session management
- Handle session cleanup and timeout
- Add session-based analytics

### **Task 7: Implement Interaction Logging**
- Add product click tracking
- Log catalog download events
- Track search refinement patterns

### **Task 8-11: Build Admin Panel**
- Create API endpoints for user data retrieval
- Build admin UI for user search and analysis
- Add data visualization and export features

## **Integration Status: ✅ COMPLETE**

Query logging is now fully integrated into the API endpoints. Every query, interaction, and session is being tracked with comprehensive context for both business intelligence and admin deep-dive capabilities.

**Ready for Task 6: Session Tracking Middleware!** 🚀
