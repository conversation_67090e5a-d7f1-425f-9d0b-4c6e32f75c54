# User Activity Database Schema Design

## Overview
This document defines the comprehensive database schema for tracking user activities, enabling admin deep-dive capabilities and business intelligence analytics.

## Design Principles
1. **User-Centric**: All activity tied to user_id (nullable for anonymous users)
2. **Temporal**: Every action timestamped for chronological analysis
3. **Contextual**: Capture enough context to understand user intent
4. **Performance**: Proper indexing for fast admin queries
5. **Privacy**: GDPR-compliant with audit trails

## Database Tables

### 1. UserQuery (user_queries)
**Purpose**: Track every search query with full context for admin deep-dive

```sql
CREATE TABLE user_queries (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,  -- Nullable for anonymous
    session_id VARCHAR(255) NOT NULL,  -- Track anonymous sessions
    
    -- Query Data
    query_text TEXT NOT NULL,
    selected_stores JSON,  -- Array of store IDs: [1, 3, 5]
    query_model VARCHAR(100),  -- AI model used: "gemini-2.5-flash-lite"
    
    -- Results & Performance
    results_count INTEGER,
    response_time_ms INTEGER,
    was_successful BOOLEAN DEFAULT TRUE,
    error_message TEXT,  -- If query failed
    
    -- Context & Classification
    query_category VARCHAR(50),  -- Auto-detected: "food", "household", etc.
    query_intent VARCHAR(50),  -- "price_comparison", "specific_product", etc.
    detected_language VARCHAR(10) DEFAULT 'da',  -- "da", "en", etc.
    
    -- Localization
    user_locale VARCHAR(10) DEFAULT 'da-DK',
    ip_country VARCHAR(2),  -- ISO country code
    timezone VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_user_queries_user_id (user_id),
    INDEX idx_user_queries_created_at (created_at),
    INDEX idx_user_queries_session_id (session_id),
    INDEX idx_user_queries_category (query_category),
    INDEX idx_user_queries_success (was_successful)
);
```

### 2. UserSession (user_sessions)
**Purpose**: Track user sessions for engagement analysis

```sql
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    
    -- Session Data
    session_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_end TIMESTAMP,
    duration_seconds INTEGER,  -- Calculated on session end
    
    -- Activity Metrics
    queries_count INTEGER DEFAULT 0,
    interactions_count INTEGER DEFAULT 0,  -- Clicks, views, etc.
    pages_viewed INTEGER DEFAULT 0,
    
    -- Technical Context
    device_type VARCHAR(20),  -- "mobile", "desktop", "tablet"
    browser VARCHAR(50),
    user_agent TEXT,
    ip_address INET,
    
    -- Engagement Quality
    bounce_session BOOLEAN DEFAULT FALSE,  -- Single query then leave
    return_session BOOLEAN DEFAULT FALSE,  -- Returning user
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_user_sessions_user_id (user_id),
    INDEX idx_user_sessions_session_id (session_id),
    INDEX idx_user_sessions_start (session_start),
    INDEX idx_user_sessions_duration (duration_seconds)
);
```

### 3. UserInteraction (user_interactions)
**Purpose**: Track specific user interactions beyond queries

```sql
CREATE TABLE user_interactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255) NOT NULL,
    
    -- Interaction Data
    interaction_type VARCHAR(50) NOT NULL,  -- "catalog_view", "product_click", "store_select"
    target_type VARCHAR(50),  -- "catalog", "product", "store"
    target_id INTEGER,  -- ID of the target (catalog_id, product_id, store_id)
    
    -- Context
    source_query_id INTEGER REFERENCES user_queries(id),  -- Which query led to this
    page_url VARCHAR(500),
    referrer_url VARCHAR(500),
    
    -- Interaction Details
    interaction_data JSON,  -- Flexible data: {"duration": 30, "scroll_depth": 0.8}
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_user_interactions_user_id (user_id),
    INDEX idx_user_interactions_type (interaction_type),
    INDEX idx_user_interactions_target (target_type, target_id),
    INDEX idx_user_interactions_created_at (created_at),
    INDEX idx_user_interactions_session_id (session_id)
);
```

### 4. UserPreferencesHistory (user_preferences_history)
**Purpose**: Track changes to user preferences over time

```sql
CREATE TABLE user_preferences_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    
    -- Preference Change Data
    preference_type VARCHAR(50) NOT NULL,  -- "preferred_stores", "locale", "currency"
    old_value TEXT,  -- JSON or string of old value
    new_value TEXT,  -- JSON or string of new value
    change_reason VARCHAR(100),  -- "user_selection", "auto_detection", "admin_update"
    
    -- Context
    changed_by_admin BOOLEAN DEFAULT FALSE,
    admin_user_id INTEGER REFERENCES users(id),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_user_prefs_history_user_id (user_id),
    INDEX idx_user_prefs_history_type (preference_type),
    INDEX idx_user_prefs_history_created_at (created_at)
);
```

## Interaction Types Reference

### Query-Related
- `query_submitted` - User submitted a search query
- `query_refined` - User modified their search
- `query_failed` - Query returned no results or error

### Catalog & Product Interactions
- `catalog_viewed` - User opened a catalog PDF
- `catalog_downloaded` - User downloaded a catalog
- `product_clicked` - User clicked on a product result
- `product_details_viewed` - User viewed product details
- `store_logo_clicked` - User clicked on store logo/selector

### Navigation & Engagement
- `page_viewed` - User visited a page
- `store_selected` - User selected/deselected stores
- `favorite_added` - User saved a favorite query
- `favorite_used` - User executed a favorite query

### Admin Actions (for audit trail)
- `admin_user_viewed` - Admin viewed user profile
- `admin_data_exported` - Admin exported user data
- `admin_preference_changed` - Admin modified user settings

## Query Categories Reference

### Product Categories
- `food_beverages` - Food and drink items
- `household_cleaning` - Cleaning supplies, detergents
- `personal_care` - Beauty, hygiene products
- `electronics` - Tech products, appliances
- `home_garden` - Home improvement, gardening
- `clothing_accessories` - Fashion items
- `health_pharmacy` - Medicine, health products
- `baby_kids` - Children's products
- `pets` - Pet food and supplies
- `seasonal` - Holiday, seasonal items

### Query Intent Types
- `price_comparison` - Looking for best price
- `specific_product` - Searching for exact item
- `category_browse` - Exploring product category
- `recipe_ingredients` - Meal planning
- `brand_search` - Looking for specific brand
- `deal_hunting` - Looking for discounts/offers

## Performance Considerations

### Indexing Strategy
1. **Primary Indexes**: user_id, created_at on all tables
2. **Query Indexes**: session_id, interaction_type, query_category
3. **Composite Indexes**: (user_id, created_at) for user timelines
4. **Partial Indexes**: Only successful queries, only logged-in users

### Data Retention
1. **Active Data**: Last 90 days in fast storage
2. **Historical Data**: 1-2 years in slower storage
3. **Aggregate Data**: Permanent summary statistics
4. **Anonymous Data**: Shorter retention (30 days)

### Privacy & Compliance
1. **User Consent**: Track consent for detailed logging
2. **Data Anonymization**: Remove PII after retention period
3. **Admin Audit Trail**: Log all admin access to user data
4. **Export/Delete**: GDPR-compliant data portability

## Admin Panel Query Examples

### User Deep Dive Queries
```sql
-- Get user's complete activity timeline
SELECT 'query' as type, created_at, query_text as details 
FROM user_queries WHERE user_id = ? 
UNION ALL
SELECT 'interaction' as type, created_at, interaction_type as details 
FROM user_interactions WHERE user_id = ?
ORDER BY created_at DESC;

-- User's store preferences over time
SELECT created_at, old_value, new_value 
FROM user_preferences_history 
WHERE user_id = ? AND preference_type = 'preferred_stores'
ORDER BY created_at;

-- User's session patterns
SELECT DATE(session_start) as date, 
       COUNT(*) as sessions,
       AVG(duration_seconds) as avg_duration,
       SUM(queries_count) as total_queries
FROM user_sessions 
WHERE user_id = ? 
GROUP BY DATE(session_start)
ORDER BY date DESC;
```

This schema provides comprehensive user activity tracking while maintaining performance and privacy compliance.
