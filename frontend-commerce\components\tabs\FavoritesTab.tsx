"use client";

import { useState, useEffect } from 'react';
import { HeartIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

interface FavoritesTabProps {
  onCountChange: (count: number) => void;
}

interface FavoriteItem {
  id: string;
  name: string;
  category: string;
  lastPrice: string;
  store: string;
  addedDate: string;
}

export default function FavoritesTab({ onCountChange }: FavoritesTabProps) {
  const [favorites, setFavorites] = useState<FavoriteItem[]>([
    // Mock data for demonstration
    {
      id: '1',
      name: 'Arla Økologisk Mælk',
      category: 'Mejeriprodukter',
      lastPrice: '12,95 kr',
      store: 'Netto',
      addedDate: '2025-01-20'
    },
    {
      id: '2',
      name: 'Kærgården Smør',
      category: 'Mejeriprodukter',
      lastPrice: '18,95 kr',
      store: 'Føtex',
      addedDate: '2025-01-19'
    }
  ]);

  const [activeCategory, setActiveCategory] = useState<string>('all');

  // Update count when favorites change
  useEffect(() => {
    onCountChange(favorites.length);
  }, [favorites, onCountChange]);

  const categories = ['all', ...Array.from(new Set(favorites.map(item => item.category)))];

  const filteredFavorites = activeCategory === 'all' 
    ? favorites 
    : favorites.filter(item => item.category === activeCategory);

  const removeFavorite = (id: string) => {
    setFavorites(prev => prev.filter(item => item.id !== id));
  };

  const searchForItem = (name: string) => {
    // TODO: Switch to search tab and search for this item
    console.log('Search for:', name);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white px-4 pt-16 pb-6 shadow-sm">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center space-x-3 mb-4">
            <HeartIconSolid className="w-6 h-6 text-red-500" />
            <h1 className="text-xl font-semibold text-gray-900">Favoritter</h1>
            {favorites.length > 0 && (
              <span className="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded-full">
                {favorites.length}
              </span>
            )}
          </div>

          {/* Category Filter */}
          {favorites.length > 0 && (
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
                    activeCategory === category
                      ? 'bg-red-100 text-red-700 border border-red-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category === 'all' ? 'Alle' : category}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6">
        {favorites.length === 0 ? (
          /* Empty State */
          <div className="max-w-2xl mx-auto text-center py-12">
            <HeartIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Ingen favoritter endnu</h3>
            <p className="text-gray-600 mb-6">
              Tryk på hjertet på produkter for at tilføje dem til dine favoritter
            </p>
            <button
              onClick={() => {
                // TODO: Switch to search tab
                console.log('Switch to search tab');
              }}
              className="bg-red-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors"
            >
              Find produkter
            </button>
          </div>
        ) : (
          /* Favorites List */
          <div className="max-w-2xl mx-auto space-y-3">
            {filteredFavorites.map((item) => (
              <div key={item.id} className="bg-white rounded-lg shadow-sm p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 mb-1">{item.name}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span className="bg-gray-100 px-2 py-1 rounded text-xs">
                        {item.category}
                      </span>
                      <span>📍 {item.store}</span>
                      <span className="font-medium text-green-600">{item.lastPrice}</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      Tilføjet {new Date(item.addedDate).toLocaleDateString('da-DK')}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => searchForItem(item.name)}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      title="Søg efter aktuelle priser"
                    >
                      <MagnifyingGlassIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => removeFavorite(item.id)}
                      className="p-2 text-red-400 hover:text-red-600 transition-colors"
                      title="Fjern fra favoritter"
                    >
                      <HeartIconSolid className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {filteredFavorites.length === 0 && activeCategory !== 'all' && (
              <div className="text-center py-8">
                <p className="text-gray-600">Ingen favoritter i kategorien "{activeCategory}"</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
