/**
 * Frontend Interaction Tracker
 * 
 * JavaScript library for easy integration of user interaction logging
 * with the Tilbudsjægeren backend. Provides simple methods to track
 * user behavior and engagement patterns.
 */

class TilbudsjaegerenTracker {
    constructor(apiBaseUrl = '') {
        this.apiBaseUrl = apiBaseUrl;
        this.currentQuery = null;
        this.queryStartTime = null;
        this.pageStartTime = Date.now();
        this.previousStores = [];
        
        // Auto-track page navigation
        this.setupPageTracking();
        
        // Auto-track errors
        this.setupErrorTracking();
    }
    
    // --- Core Logging Methods ---
    
    async logInteraction(endpoint, data) {
        try {
            const response = await fetch(`${this.apiBaseUrl}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
                credentials: 'include' // Include cookies for session tracking
            });
            
            if (!response.ok) {
                console.warn(`Failed to log interaction: ${endpoint}`, response.status);
            }
            
            return await response.json();
        } catch (error) {
            console.warn('Error logging interaction:', error);
            return null;
        }
    }
    
    // --- Product Interaction Tracking ---
    
    logProductClick(productId, productName, options = {}) {
        const data = {
            product_id: productId,
            product_name: productName,
            product_price: options.price,
            store_id: options.storeId,
            store_name: options.storeName,
            source_query_id: options.sourceQueryId || this.currentQuery?.id,
            position_in_results: options.position
        };
        
        return this.logInteraction('/log-product-click', data);
    }
    
    logProductDetailsView(productId, productName, viewDurationSeconds = null) {
        const data = {
            product_id: productId,
            product_name: productName,
            view_duration_seconds: viewDurationSeconds,
            source_query_id: this.currentQuery?.id
        };
        
        return this.logInteraction('/log-product-details', data);
    }
    
    // --- Catalog Interaction Tracking ---
    
    logCatalogDownload(catalogId, catalogTitle, storeId, storeName, fileSizeBytes = null) {
        const data = {
            catalog_id: catalogId,
            catalog_title: catalogTitle,
            store_id: storeId,
            store_name: storeName,
            file_size_bytes: fileSizeBytes,
            source_query_id: this.currentQuery?.id
        };
        
        return this.logInteraction('/log-catalog-download', data);
    }
    
    // --- Search Behavior Tracking ---
    
    setCurrentQuery(queryId, queryText) {
        this.currentQuery = {
            id: queryId,
            text: queryText,
            startTime: Date.now()
        };
        this.queryStartTime = Date.now();
    }
    
    logQueryRefinement(newQuery, refinementType = 'manual') {
        if (!this.currentQuery) return;
        
        const timeBetweenQueries = this.queryStartTime ? 
            Math.floor((Date.now() - this.queryStartTime) / 1000) : null;
        
        const data = {
            original_query: this.currentQuery.text,
            refined_query: newQuery,
            original_query_id: this.currentQuery.id,
            refinement_type: refinementType,
            time_between_queries_seconds: timeBetweenQueries
        };
        
        return this.logInteraction('/log-query-refinement', data);
    }
    
    logSearchSuggestionClick(suggestionText, position, originalQuery) {
        const data = {
            suggestion_text: suggestionText,
            suggestion_position: position,
            original_query: originalQuery
        };
        
        return this.logInteraction('/log-search-suggestion-click', data);
    }
    
    // --- Store Filter Tracking ---
    
    logStoreFilterChange(newStores, changeType = 'manual') {
        const data = {
            previous_stores: this.previousStores,
            new_stores: newStores,
            change_type: changeType
        };
        
        this.previousStores = [...newStores]; // Update for next change
        
        return this.logInteraction('/log-store-filter-change', data);
    }
    
    // --- UI Element Tracking ---
    
    logUIInteraction(elementType, elementId, action = 'click', options = {}) {
        const data = {
            element_type: elementType,
            element_id: elementId,
            action: action,
            element_text: options.text,
            page_url: window.location.href
        };
        
        return this.logInteraction('/log-ui-interaction', data);
    }
    
    // --- Error Tracking ---
    
    logError(errorType, errorMessage, userAction = null) {
        const data = {
            error_type: errorType,
            error_message: errorMessage,
            page_url: window.location.href,
            user_action_before_error: userAction
        };
        
        return this.logInteraction('/log-error-encounter', data);
    }
    
    // --- Automatic Tracking Setup ---
    
    setupPageTracking() {
        // Track page navigation
        let previousUrl = window.location.href;
        
        const trackNavigation = () => {
            const currentUrl = window.location.href;
            if (currentUrl !== previousUrl) {
                const timeOnPage = Math.floor((Date.now() - this.pageStartTime) / 1000);
                
                // Log navigation (you might want to implement this endpoint)
                // this.logPageNavigation(previousUrl, currentUrl, timeOnPage);
                
                previousUrl = currentUrl;
                this.pageStartTime = Date.now();
            }
        };
        
        // Check for navigation changes
        setInterval(trackNavigation, 1000);
        
        // Track back/forward button usage
        window.addEventListener('popstate', trackNavigation);
    }
    
    setupErrorTracking() {
        // Track JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError('javascript', event.message, 'page_interaction');
        });
        
        // Track unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError('promise_rejection', event.reason, 'async_operation');
        });
    }
    
    // --- Convenience Methods for Common Patterns ---
    
    trackProductGrid(containerSelector) {
        const container = document.querySelector(containerSelector);
        if (!container) return;
        
        container.addEventListener('click', (event) => {
            const productElement = event.target.closest('[data-product-id]');
            if (productElement) {
                const productId = parseInt(productElement.dataset.productId);
                const productName = productElement.dataset.productName || '';
                const position = Array.from(container.children).indexOf(productElement) + 1;
                
                this.logProductClick(productId, productName, { position });
            }
        });
    }
    
    trackCatalogLinks(containerSelector) {
        const container = document.querySelector(containerSelector);
        if (!container) return;
        
        container.addEventListener('click', (event) => {
            const catalogLink = event.target.closest('[data-catalog-id]');
            if (catalogLink) {
                const catalogId = parseInt(catalogLink.dataset.catalogId);
                const catalogTitle = catalogLink.dataset.catalogTitle || '';
                const storeId = parseInt(catalogLink.dataset.storeId);
                const storeName = catalogLink.dataset.storeName || '';
                
                // If it's a download link
                if (catalogLink.href && catalogLink.href.includes('.pdf')) {
                    this.logCatalogDownload(catalogId, catalogTitle, storeId, storeName);
                }
            }
        });
    }
    
    trackStoreSelector(selectorElement) {
        if (!selectorElement) return;
        
        selectorElement.addEventListener('change', () => {
            const selectedStores = Array.from(selectorElement.querySelectorAll('input:checked'))
                .map(input => parseInt(input.value));
            
            this.logStoreFilterChange(selectedStores);
        });
    }
    
    trackSearchForm(formSelector) {
        const form = document.querySelector(formSelector);
        if (!form) return;
        
        const searchInput = form.querySelector('input[type="search"], input[name="query"]');
        let lastQuery = '';
        
        form.addEventListener('submit', (event) => {
            const currentQuery = searchInput.value.trim();
            
            // Check if this is a query refinement
            if (lastQuery && lastQuery !== currentQuery) {
                this.logQueryRefinement(currentQuery);
            }
            
            lastQuery = currentQuery;
        });
    }
    
    // --- Performance Tracking ---
    
    trackSlowResponses(thresholdMs = 3000) {
        // Override fetch to track response times
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const startTime = Date.now();
            
            try {
                const response = await originalFetch(...args);
                const responseTime = Date.now() - startTime;
                
                if (responseTime > thresholdMs) {
                    // Log slow response (you might want to implement this endpoint)
                    console.warn(`Slow response detected: ${responseTime}ms for ${args[0]}`);
                }
                
                return response;
            } catch (error) {
                const responseTime = Date.now() - startTime;
                this.logError('fetch_error', error.message, 'api_request');
                throw error;
            }
        };
    }
}

// --- Global Instance and Easy Setup ---

// Create global tracker instance
window.tilbudsjaegerenTracker = new TilbudsjaegerenTracker();

// Easy setup function for common tracking
window.setupTilbudsjaegerenTracking = function(options = {}) {
    const tracker = window.tilbudsjaegerenTracker;
    
    // Track product grids
    if (options.productGridSelector) {
        tracker.trackProductGrid(options.productGridSelector);
    }
    
    // Track catalog links
    if (options.catalogLinksSelector) {
        tracker.trackCatalogLinks(options.catalogLinksSelector);
    }
    
    // Track store selector
    if (options.storeSelectorSelector) {
        const storeSelector = document.querySelector(options.storeSelectorSelector);
        tracker.trackStoreSelector(storeSelector);
    }
    
    // Track search form
    if (options.searchFormSelector) {
        tracker.trackSearchForm(options.searchFormSelector);
    }
    
    // Track slow responses
    if (options.trackSlowResponses !== false) {
        tracker.trackSlowResponses(options.slowResponseThreshold);
    }
};

// Auto-setup with common selectors if they exist
document.addEventListener('DOMContentLoaded', () => {
    window.setupTilbudsjaegerenTracking({
        productGridSelector: '.product-grid, .products-container',
        catalogLinksSelector: '.catalog-links, .catalogs-container',
        storeSelectorSelector: '.store-selector, .store-filters',
        searchFormSelector: '.search-form, form[action*="ask"]'
    });
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TilbudsjaegerenTracker;
}
