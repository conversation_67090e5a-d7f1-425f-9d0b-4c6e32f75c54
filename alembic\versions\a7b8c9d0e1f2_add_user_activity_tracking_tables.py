"""add_user_activity_tracking_tables

Revision ID: a7b8c9d0e1f2
Revises: 769079bbbb19
Create Date: 2025-07-23 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a7b8c9d0e1f2'
down_revision: Union[str, None] = '769079bbbb19'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add comprehensive user activity tracking tables."""
    
    # Create user_queries table
    op.create_table(
        'user_queries',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('session_id', sa.String(length=255), nullable=False),
        
        # Query Data
        sa.Column('query_text', sa.Text(), nullable=False),
        sa.Column('selected_stores', sa.JSON(), nullable=True),
        sa.Column('query_model', sa.String(length=100), nullable=True),
        
        # Results & Performance
        sa.Column('results_count', sa.Integer(), nullable=True),
        sa.Column('response_time_ms', sa.Integer(), nullable=True),
        sa.Column('was_successful', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('error_message', sa.Text(), nullable=True),
        
        # Context & Classification
        sa.Column('query_category', sa.String(length=50), nullable=True),
        sa.Column('query_intent', sa.String(length=50), nullable=True),
        sa.Column('detected_language', sa.String(length=10), nullable=False, server_default='da'),
        
        # Localization
        sa.Column('user_locale', sa.String(length=10), nullable=False, server_default='da-DK'),
        sa.Column('ip_country', sa.String(length=2), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=True),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        
        # Foreign Keys
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    )
    
    # Create indexes for user_queries
    op.create_index('idx_user_queries_user_id', 'user_queries', ['user_id'])
    op.create_index('idx_user_queries_created_at', 'user_queries', ['created_at'])
    op.create_index('idx_user_queries_session_id', 'user_queries', ['session_id'])
    op.create_index('idx_user_queries_category', 'user_queries', ['query_category'])
    op.create_index('idx_user_queries_success', 'user_queries', ['was_successful'])
    
    # Create user_sessions table
    op.create_table(
        'user_sessions',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('session_id', sa.String(length=255), nullable=False, unique=True),
        
        # Session Data
        sa.Column('session_start', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        sa.Column('session_end', sa.DateTime(), nullable=True),
        sa.Column('duration_seconds', sa.Integer(), nullable=True),
        
        # Activity Metrics
        sa.Column('queries_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('interactions_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('pages_viewed', sa.Integer(), nullable=False, server_default='0'),
        
        # Technical Context
        sa.Column('device_type', sa.String(length=20), nullable=True),
        sa.Column('browser', sa.String(length=50), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        
        # Engagement Quality
        sa.Column('bounce_session', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('return_session', sa.Boolean(), nullable=False, server_default='false'),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        
        # Foreign Keys
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    )
    
    # Create indexes for user_sessions
    op.create_index('idx_user_sessions_user_id', 'user_sessions', ['user_id'])
    op.create_index('idx_user_sessions_session_id', 'user_sessions', ['session_id'])
    op.create_index('idx_user_sessions_start', 'user_sessions', ['session_start'])
    op.create_index('idx_user_sessions_duration', 'user_sessions', ['duration_seconds'])
    
    # Create user_interactions table
    op.create_table(
        'user_interactions',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('session_id', sa.String(length=255), nullable=False),
        
        # Interaction Data
        sa.Column('interaction_type', sa.String(length=50), nullable=False),
        sa.Column('target_type', sa.String(length=50), nullable=True),
        sa.Column('target_id', sa.Integer(), nullable=True),
        
        # Context
        sa.Column('source_query_id', sa.Integer(), nullable=True),
        sa.Column('page_url', sa.String(length=500), nullable=True),
        sa.Column('referrer_url', sa.String(length=500), nullable=True),
        
        # Interaction Details
        sa.Column('interaction_data', sa.JSON(), nullable=True),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        
        # Foreign Keys
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['source_query_id'], ['user_queries.id'], ondelete='SET NULL'),
    )
    
    # Create indexes for user_interactions
    op.create_index('idx_user_interactions_user_id', 'user_interactions', ['user_id'])
    op.create_index('idx_user_interactions_type', 'user_interactions', ['interaction_type'])
    op.create_index('idx_user_interactions_target', 'user_interactions', ['target_type', 'target_id'])
    op.create_index('idx_user_interactions_created_at', 'user_interactions', ['created_at'])
    op.create_index('idx_user_interactions_session_id', 'user_interactions', ['session_id'])
    
    # Create user_preferences_history table
    op.create_table(
        'user_preferences_history',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        
        # Preference Change Data
        sa.Column('preference_type', sa.String(length=50), nullable=False),
        sa.Column('old_value', sa.Text(), nullable=True),
        sa.Column('new_value', sa.Text(), nullable=True),
        sa.Column('change_reason', sa.String(length=100), nullable=True),
        
        # Context
        sa.Column('changed_by_admin', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('admin_user_id', sa.Integer(), nullable=True),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        
        # Foreign Keys
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['admin_user_id'], ['users.id']),
    )
    
    # Create indexes for user_preferences_history
    op.create_index('idx_user_prefs_history_user_id', 'user_preferences_history', ['user_id'])
    op.create_index('idx_user_prefs_history_type', 'user_preferences_history', ['preference_type'])
    op.create_index('idx_user_prefs_history_created_at', 'user_preferences_history', ['created_at'])
    
    # Create admin_audit_logs table
    op.create_table(
        'admin_audit_logs',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('admin_user_id', sa.Integer(), nullable=False),
        sa.Column('target_user_id', sa.Integer(), nullable=True),
        
        # Audit Data
        sa.Column('action_type', sa.String(length=50), nullable=False),
        sa.Column('resource_accessed', sa.String(length=100), nullable=True),
        sa.Column('action_details', sa.JSON(), nullable=True),
        
        # Technical Context
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        
        # Foreign Keys
        sa.ForeignKeyConstraint(['admin_user_id'], ['users.id']),
        sa.ForeignKeyConstraint(['target_user_id'], ['users.id']),
    )
    
    # Create indexes for admin_audit_logs
    op.create_index('idx_admin_audit_logs_admin_user_id', 'admin_audit_logs', ['admin_user_id'])
    op.create_index('idx_admin_audit_logs_target_user_id', 'admin_audit_logs', ['target_user_id'])
    op.create_index('idx_admin_audit_logs_action_type', 'admin_audit_logs', ['action_type'])
    op.create_index('idx_admin_audit_logs_created_at', 'admin_audit_logs', ['created_at'])


def downgrade() -> None:
    """Remove user activity tracking tables."""
    
    # Drop indexes for admin_audit_logs
    op.drop_index('idx_admin_audit_logs_created_at', table_name='admin_audit_logs')
    op.drop_index('idx_admin_audit_logs_action_type', table_name='admin_audit_logs')
    op.drop_index('idx_admin_audit_logs_target_user_id', table_name='admin_audit_logs')
    op.drop_index('idx_admin_audit_logs_admin_user_id', table_name='admin_audit_logs')
    
    # Drop indexes for user_preferences_history
    op.drop_index('idx_user_prefs_history_created_at', table_name='user_preferences_history')
    op.drop_index('idx_user_prefs_history_type', table_name='user_preferences_history')
    op.drop_index('idx_user_prefs_history_user_id', table_name='user_preferences_history')
    
    # Drop indexes for user_interactions
    op.drop_index('idx_user_interactions_session_id', table_name='user_interactions')
    op.drop_index('idx_user_interactions_created_at', table_name='user_interactions')
    op.drop_index('idx_user_interactions_target', table_name='user_interactions')
    op.drop_index('idx_user_interactions_type', table_name='user_interactions')
    op.drop_index('idx_user_interactions_user_id', table_name='user_interactions')
    
    # Drop indexes for user_sessions
    op.drop_index('idx_user_sessions_duration', table_name='user_sessions')
    op.drop_index('idx_user_sessions_start', table_name='user_sessions')
    op.drop_index('idx_user_sessions_session_id', table_name='user_sessions')
    op.drop_index('idx_user_sessions_user_id', table_name='user_sessions')
    
    # Drop indexes for user_queries
    op.drop_index('idx_user_queries_success', table_name='user_queries')
    op.drop_index('idx_user_queries_category', table_name='user_queries')
    op.drop_index('idx_user_queries_session_id', table_name='user_queries')
    op.drop_index('idx_user_queries_created_at', table_name='user_queries')
    op.drop_index('idx_user_queries_user_id', table_name='user_queries')
    
    # Drop tables (in reverse order of creation due to foreign keys)
    op.drop_table('admin_audit_logs')
    op.drop_table('user_preferences_history')
    op.drop_table('user_interactions')
    op.drop_table('user_sessions')
    op.drop_table('user_queries')
