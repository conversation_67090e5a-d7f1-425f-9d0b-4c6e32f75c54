"""merge_user_activity_migration

Revision ID: e83e1dc1339f
Revises: 26696f191266, a7b8c9d0e1f2
Create Date: 2025-07-23 21:02:11.040261

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e83e1dc1339f'
down_revision: Union[str, None] = ('26696f191266', 'a7b8c9d0e1f2')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
