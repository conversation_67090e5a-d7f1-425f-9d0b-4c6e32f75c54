"use client";

import { useState } from 'react';
import BottomTabNavigation, { TabType } from '@/components/layout/BottomTabNavigation';
import HamburgerMenu from '@/components/layout/HamburgerMenu';

export default function DemoPage() {
  const [activeTab, setActiveTab] = useState<TabType>('search');
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    if (isMenuOpen) {
      setIsMenuOpen(false);
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'search':
        return (
          <div className="p-6 space-y-6">
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">🔍</span>
              </div>
              <h1 className="text-xl font-medium text-gray-800 mb-2">Find de bedste tilbud</h1>
              <p className="text-gray-600 text-sm">Skriv hvad du søger, og vi finder de billigste priser</p>
            </div>
            
            <div className="relative">
              <input
                type="text"
                placeholder="Søg efter produkter... (f.eks. 'økologisk mælk')"
                className="w-full px-6 py-4 text-lg border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button className="absolute right-2 top-2 px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors">
                Søg
              </button>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Populære søgninger</h3>
              <div className="flex flex-wrap gap-2">
                {['økologisk mælk', 'billigt brød', 'kaffe tilbud', 'friske æg'].map((search) => (
                  <button
                    key={search}
                    className="px-3 py-2 bg-blue-50 text-blue-700 rounded-full text-sm border border-blue-200 hover:bg-blue-100 transition-colors"
                  >
                    {search}
                  </button>
                ))}
              </div>
            </div>
          </div>
        );
      case 'shopping':
        return (
          <div className="p-6 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span className="text-2xl">🛒</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Indkøbsliste</h2>
            <p className="text-gray-600 mb-6">Produkter du tilføjer fra søgeresultater vil vises her</p>
            <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors">
              Start med at søge
            </button>
          </div>
        );
      case 'favorites':
        return (
          <div className="p-6 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span className="text-2xl">❤️</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Favoritter</h2>
            <p className="text-gray-600 mb-6">Tryk på hjertet på produkter for at tilføje dem til dine favoritter</p>
            <button className="bg-red-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors">
              Find produkter
            </button>
          </div>
        );
      case 'profile':
        return (
          <div className="p-6">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">👤</span>
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Jonas</h2>
              <p className="text-gray-600"><EMAIL></p>
            </div>
            
            <div className="space-y-3">
              {[
                { icon: '👤', label: 'Rediger profil', desc: 'Navn, email og præferencer' },
                { icon: '⚙️', label: 'Indstillinger', desc: 'App indstillinger' },
                { icon: '📊', label: 'Mine data', desc: 'Se dine søgninger' },
                { icon: '🛡️', label: 'Privatliv', desc: 'Privatlivspolitik' },
                { icon: '❓', label: 'Hjælp', desc: 'FAQ og support' }
              ].map((item) => (
                <button
                  key={item.label}
                  className="w-full bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors text-left"
                >
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span>{item.icon}</span>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{item.label}</div>
                    <div className="text-sm text-gray-500">{item.desc}</div>
                  </div>
                  <span className="text-gray-400">›</span>
                </button>
              ))}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Hamburger Menu */}
      <HamburgerMenu isOpen={isMenuOpen} onToggle={toggleMenu} />
      
      {/* Header */}
      <div className="bg-white px-4 pt-16 pb-4 shadow-sm">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-blue-600">🦉 Tilbudsjægeren</h1>
          <p className="text-sm text-gray-600">Mobile-First Demo</p>
        </div>
      </div>
      
      {/* Main Content Area */}
      <main className="flex-1 pb-20 overflow-y-auto">
        {renderTabContent()}
      </main>
      
      {/* Bottom Tab Navigation */}
      <BottomTabNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        shoppingListCount={3}
        favoritesCount={2}
      />
    </div>
  );
}
