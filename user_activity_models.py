"""
SQLAlchemy Models for User Activity Tracking

These models will be integrated into database.py to enable comprehensive
user activity logging and admin deep-dive capabilities.
"""

from sqlalchemy import Column, Integer, String, Float, ForeignKey, DateTime, Text, JSON, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

# These classes will be added to database.py

class UserQuery(Base):
    """
    Track every search query with full context for admin deep-dive
    Enables analysis of user search patterns, preferences, and behavior
    """
    __tablename__ = "user_queries"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True)
    session_id = Column(String(255), nullable=False, index=True)
    
    # Query Data
    query_text = Column(Text, nullable=False)
    selected_stores = Column(JSON, nullable=True)  # Array of store IDs: [1, 3, 5]
    query_model = Column(String(100), nullable=True)  # AI model used
    
    # Results & Performance
    results_count = Column(Integer, nullable=True)
    response_time_ms = Column(Integer, nullable=True)
    was_successful = Column(Boolean, default=True, index=True)
    error_message = Column(Text, nullable=True)
    
    # Context & Classification
    query_category = Column(String(50), nullable=True, index=True)  # "food", "household", etc.
    query_intent = Column(String(50), nullable=True)  # "price_comparison", "specific_product"
    detected_language = Column(String(10), default='da')
    
    # Localization
    user_locale = Column(String(10), default='da-DK')
    ip_country = Column(String(2), nullable=True)  # ISO country code
    timezone = Column(String(50), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # Relationships
    user = relationship("User", backref="queries")
    interactions = relationship("UserInteraction", back_populates="source_query")


class UserSession(Base):
    """
    Track user sessions for engagement analysis
    Enables understanding of user engagement patterns and session quality
    """
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True)
    session_id = Column(String(255), unique=True, nullable=False, index=True)
    
    # Session Data
    session_start = Column(DateTime, default=datetime.utcnow, index=True)
    session_end = Column(DateTime, nullable=True)
    duration_seconds = Column(Integer, nullable=True, index=True)
    
    # Activity Metrics
    queries_count = Column(Integer, default=0)
    interactions_count = Column(Integer, default=0)
    pages_viewed = Column(Integer, default=0)
    
    # Technical Context
    device_type = Column(String(20), nullable=True)  # "mobile", "desktop", "tablet"
    browser = Column(String(50), nullable=True)
    user_agent = Column(Text, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    
    # Engagement Quality
    bounce_session = Column(Boolean, default=False)  # Single query then leave
    return_session = Column(Boolean, default=False)  # Returning user
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", backref="sessions")


class UserInteraction(Base):
    """
    Track specific user interactions beyond queries
    Enables understanding of what users actually engage with
    """
    __tablename__ = "user_interactions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True)
    session_id = Column(String(255), nullable=False, index=True)
    
    # Interaction Data
    interaction_type = Column(String(50), nullable=False, index=True)
    target_type = Column(String(50), nullable=True)  # "catalog", "product", "store"
    target_id = Column(Integer, nullable=True)
    
    # Context
    source_query_id = Column(Integer, ForeignKey("user_queries.id", ondelete="SET NULL"), nullable=True)
    page_url = Column(String(500), nullable=True)
    referrer_url = Column(String(500), nullable=True)
    
    # Interaction Details (flexible JSON data)
    interaction_data = Column(JSON, nullable=True)  # {"duration": 30, "scroll_depth": 0.8}
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # Relationships
    user = relationship("User", backref="interactions")
    source_query = relationship("UserQuery", back_populates="interactions")
    
    # Composite index for target lookups
    __table_args__ = (
        Index('idx_user_interactions_target', 'target_type', 'target_id'),
    )


class UserPreferencesHistory(Base):
    """
    Track changes to user preferences over time
    Enables understanding of how user preferences evolve
    """
    __tablename__ = "user_preferences_history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Preference Change Data
    preference_type = Column(String(50), nullable=False, index=True)
    old_value = Column(Text, nullable=True)  # JSON or string
    new_value = Column(Text, nullable=True)  # JSON or string
    change_reason = Column(String(100), nullable=True)  # "user_selection", "auto_detection"
    
    # Context
    changed_by_admin = Column(Boolean, default=False)
    admin_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], backref="preference_history")
    admin_user = relationship("User", foreign_keys=[admin_user_id])


class AdminAuditLog(Base):
    """
    Track admin access to user data for compliance and security
    GDPR compliance requires logging who accessed what user data when
    """
    __tablename__ = "admin_audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    admin_user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    target_user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    
    # Audit Data
    action_type = Column(String(50), nullable=False, index=True)  # "user_viewed", "data_exported"
    resource_accessed = Column(String(100), nullable=True)  # "query_history", "session_data"
    action_details = Column(JSON, nullable=True)  # Additional context
    
    # Technical Context
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # Relationships
    admin_user = relationship("User", foreign_keys=[admin_user_id])
    target_user = relationship("User", foreign_keys=[target_user_id])


# Enums and Constants for reference

INTERACTION_TYPES = {
    # Query-Related
    'query_submitted': 'User submitted a search query',
    'query_refined': 'User modified their search',
    'query_failed': 'Query returned no results or error',
    
    # Catalog & Product Interactions
    'catalog_viewed': 'User opened a catalog PDF',
    'catalog_downloaded': 'User downloaded a catalog',
    'product_clicked': 'User clicked on a product result',
    'product_details_viewed': 'User viewed product details',
    'store_logo_clicked': 'User clicked on store logo/selector',
    
    # Navigation & Engagement
    'page_viewed': 'User visited a page',
    'store_selected': 'User selected/deselected stores',
    'favorite_added': 'User saved a favorite query',
    'favorite_used': 'User executed a favorite query',
    
    # Admin Actions
    'admin_user_viewed': 'Admin viewed user profile',
    'admin_data_exported': 'Admin exported user data',
    'admin_preference_changed': 'Admin modified user settings'
}

QUERY_CATEGORIES = {
    'food_beverages': 'Food and drink items',
    'household_cleaning': 'Cleaning supplies, detergents',
    'personal_care': 'Beauty, hygiene products',
    'electronics': 'Tech products, appliances',
    'home_garden': 'Home improvement, gardening',
    'clothing_accessories': 'Fashion items',
    'health_pharmacy': 'Medicine, health products',
    'baby_kids': 'Children\'s products',
    'pets': 'Pet food and supplies',
    'seasonal': 'Holiday, seasonal items'
}

QUERY_INTENTS = {
    'price_comparison': 'Looking for best price',
    'specific_product': 'Searching for exact item',
    'category_browse': 'Exploring product category',
    'recipe_ingredients': 'Meal planning',
    'brand_search': 'Looking for specific brand',
    'deal_hunting': 'Looking for discounts/offers'
}

PREFERENCE_TYPES = {
    'preferred_stores': 'User\'s preferred store selection',
    'locale': 'User\'s language and country preference',
    'currency': 'User\'s preferred currency',
    'query_model': 'User\'s preferred AI model',
    'ui_theme': 'User\'s interface theme preference',
    'notification_settings': 'User\'s notification preferences'
}
