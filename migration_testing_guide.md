# User Activity Tracking Migration Testing Guide

## Migration File Created ✅

**File**: `alembic/versions/a7b8c9d0e1f2_add_user_activity_tracking_tables.py`
**Revision ID**: `a7b8c9d0e1f2`
**Revises**: `769079bbbb19` (latest migration)

## Tables Created by Migration

### 1. **user_queries** - Query Tracking
- Tracks every search query with full context
- Links to users (nullable for anonymous)
- Includes performance metrics and categorization
- **Indexes**: user_id, created_at, session_id, category, success

### 2. **user_sessions** - Session Tracking  
- Tracks user sessions for engagement analysis
- Includes device/browser information
- Measures session quality (bounce, return visits)
- **Indexes**: user_id, session_id, start_time, duration

### 3. **user_interactions** - Interaction Tracking
- Tracks clicks, views, engagement beyond queries
- Flexible JSON data for extensible tracking
- Links back to source queries
- **Indexes**: user_id, type, target, created_at, session_id

### 4. **user_preferences_history** - Preference Changes
- Tracks how user preferences evolve over time
- Includes admin change tracking
- Supports audit trail for settings
- **Indexes**: user_id, type, created_at

### 5. **admin_audit_logs** - GDPR Compliance
- Tracks admin access to user data
- Required for GDPR compliance
- Includes IP and user agent for security
- **Indexes**: admin_user_id, target_user_id, action_type, created_at

## Testing the Migration

### **IMPORTANT: Test on Development Database First!**

```bash
# 1. Check current migration status
alembic current

# 2. Check what migrations are pending
alembic heads

# 3. Test the migration (DRY RUN)
alembic upgrade a7b8c9d0e1f2 --sql

# 4. Apply the migration
alembic upgrade head

# 5. Verify tables were created
# Connect to your database and check:
\dt user_*
\dt admin_*
```

### **Rollback Testing**
```bash
# Test rollback (if needed)
alembic downgrade 769079bbbb19

# Re-apply if rollback worked
alembic upgrade head
```

## Database Schema Verification

After migration, verify these tables exist with proper structure:

### **user_queries Table**
```sql
-- Should have these key columns:
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_queries' 
ORDER BY ordinal_position;

-- Key columns to verify:
-- id (integer, not null)
-- user_id (integer, nullable) 
-- session_id (varchar(255), not null)
-- query_text (text, not null)
-- selected_stores (json, nullable)
-- created_at (timestamp, not null)
```

### **Indexes Verification**
```sql
-- Check indexes were created properly
SELECT indexname, tablename 
FROM pg_indexes 
WHERE tablename LIKE 'user_%' OR tablename = 'admin_audit_logs'
ORDER BY tablename, indexname;

-- Should see indexes like:
-- idx_user_queries_user_id
-- idx_user_queries_created_at
-- idx_user_sessions_session_id
-- etc.
```

### **Foreign Key Verification**
```sql
-- Check foreign key constraints
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name LIKE 'user_%' OR tc.table_name = 'admin_audit_logs';
```

## Expected Results

### **Tables Created**: 5 new tables
- ✅ user_queries
- ✅ user_sessions  
- ✅ user_interactions
- ✅ user_preferences_history
- ✅ admin_audit_logs

### **Indexes Created**: 20 indexes total
- ✅ 5 indexes on user_queries
- ✅ 4 indexes on user_sessions
- ✅ 5 indexes on user_interactions  
- ✅ 3 indexes on user_preferences_history
- ✅ 4 indexes on admin_audit_logs

### **Foreign Keys**: 8 foreign key constraints
- ✅ user_queries → users(id)
- ✅ user_sessions → users(id)
- ✅ user_interactions → users(id)
- ✅ user_interactions → user_queries(id)
- ✅ user_preferences_history → users(id) [2 constraints]
- ✅ admin_audit_logs → users(id) [2 constraints]

## Troubleshooting

### **Common Issues**

1. **Migration fails with "relation already exists"**
   - Check if tables already exist: `\dt user_*`
   - If they exist, you may need to manually drop them first

2. **Foreign key constraint fails**
   - Ensure the `users` table exists and has proper structure
   - Check that user IDs in existing data are valid

3. **Index creation fails**
   - Check for naming conflicts with existing indexes
   - Verify column names match exactly

### **Safe Recovery**
```bash
# If migration fails partway through:
# 1. Check what was created
\dt user_*

# 2. Manually clean up if needed
DROP TABLE IF EXISTS user_interactions CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS user_queries CASCADE;

# 3. Try migration again
alembic upgrade head
```

## Next Steps After Migration

1. **Verify Migration Success** ✅
2. **Update database.py** with new model classes
3. **Create logging service** to populate these tables
4. **Test data insertion** with sample queries
5. **Build admin API endpoints** to read the data

## Migration Status: ✅ READY FOR TESTING

The migration file is complete and ready for testing on your development database. 

**Remember**: Always test migrations on development first, never directly on production!

**Ready to run the migration?**
