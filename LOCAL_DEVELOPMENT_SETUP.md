# Local Development Setup Guide

## 🚀 **Complete Local Development Environment**

This guide will help you set up and debug the complete user activity tracking system on your local development server.

---

## 📋 **Prerequisites**

### **Required Software**
- Python 3.8+ with pip
- PostgreSQL 12+ (local or remote)
- Node.js 16+ (for frontend tools, optional)
- Git for version control
- Code editor (VS Code recommended)

### **Python Dependencies**
```bash
# Core FastAPI dependencies (should already be installed)
pip install fastapi uvicorn sqlalchemy psycopg2-binary alembic
pip install python-jose[cryptography] passlib[bcrypt]
pip install python-multipart requests

# Additional dependencies for user activity system
pip install python-dateutil
```

---

## 🗄️ **Database Setup**

### **1. Verify Database Connection**
```bash
# Test your existing database connection
python -c "
from database import engine
try:
    with engine.connect() as conn:
        result = conn.execute('SELECT version();')
        print('Database connected:', result.fetchone()[0])
except Exception as e:
    print('Database error:', e)
"
```

### **2. Run User Activity Migration**
```bash
# Run the migration to create new tables
alembic upgrade head

# Verify tables were created
python -c "
from database import engine
with engine.connect() as conn:
    result = conn.execute(\"\"\"
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name LIKE 'user_%'
    \"\"\")
    tables = [row[0] for row in result.fetchall()]
    print('User activity tables:', tables)
"
```

### **3. Check Table Structure**
```sql
-- Connect to your database and verify table structure
\d user_queries
\d user_sessions  
\d user_interactions
\d user_preferences_history
\d admin_audit_log
```

---

## 🔧 **Backend Configuration**

### **1. Environment Variables**
Create a `.env` file in your project root:
```bash
# Database (use your existing connection)
DATABASE_URL=postgresql://username:password@localhost/tilbudsjaegeren

# Admin authentication
ADMIN_JWT_SECRET=your_super_secret_key_for_development
ADMIN_TOKEN_EXPIRE_HOURS=24

# Session tracking
SESSION_TIMEOUT_MINUTES=30
SESSION_CLEANUP_INTERVAL_HOURS=24

# Development settings
DEBUG=True
ENVIRONMENT=development
```

### **2. Verify API Integration**
```bash
# Start your FastAPI server
uvicorn api:app --reload --host 0.0.0.0 --port 8000

# In another terminal, test the new endpoints
curl http://localhost:8000/api/admin/session-stats
# Should return session statistics (may be empty initially)
```

### **3. Test Session Middleware**
```bash
# Make a request to trigger session tracking
curl -c cookies.txt http://localhost:8000/stores

# Check if session cookie was set
cat cookies.txt
# Should show tilbudsjaegeren_session cookie

# Verify session in database
python -c "
from database import get_db, UserSession
db = next(get_db())
sessions = db.query(UserSession).all()
print(f'Active sessions: {len(sessions)}')
for session in sessions[-3:]:  # Show last 3
    print(f'Session {session.session_id}: {session.session_start}')
db.close()
"
```

---

## 🎯 **Testing User Activity Logging**

### **1. Test Query Logging**
```bash
# Make a search request
curl -X POST http://localhost:8000/ask \
  -H "Content-Type: application/json" \
  -d '{"query": "pizza tilbud", "selected_stores": [1, 2, 3]}'

# Verify query was logged
python -c "
from database import get_db, UserQuery
db = next(get_db())
queries = db.query(UserQuery).order_by(UserQuery.created_at.desc()).limit(5).all()
print(f'Recent queries: {len(queries)}')
for query in queries:
    print(f'Query: {query.query_text} - Success: {query.was_successful}')
db.close()
"
```

### **2. Test Interaction Logging**
```bash
# Test product click logging
curl -X POST http://localhost:8000/log-product-click \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 123,
    "product_name": "Test Pizza",
    "product_price": 29.95,
    "store_id": 1,
    "store_name": "Test Store",
    "position_in_results": 1
  }'

# Verify interaction was logged
python -c "
from database import get_db, UserInteraction
db = next(get_db())
interactions = db.query(UserInteraction).order_by(UserInteraction.created_at.desc()).limit(3).all()
print(f'Recent interactions: {len(interactions)}')
for interaction in interactions:
    print(f'Interaction: {interaction.interaction_type} - Target: {interaction.target_id}')
db.close()
"
```

---

## 👤 **Admin System Setup**

### **1. Create Admin User**
```python
# Run this Python script to create an admin user
from database import get_db, User
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
db = next(get_db())

# Create admin user
admin_user = User(
    username="<EMAIL>",
    hashed_password=pwd_context.hash("admin123"),  # Change this password!
    is_admin=True
)

db.add(admin_user)
db.commit()
print(f"Admin user created with ID: {admin_user.id}")
db.close()
```

### **2. Get Admin Token**
```bash
# Login to get JWT token
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "admin123"
  }'

# Save the access_token from the response
export ADMIN_TOKEN="your_access_token_here"
```

### **3. Test Admin API Endpoints**
```bash
# Test user search
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  "http://localhost:8000/api/admin/users/search?limit=5"

# Test system analytics
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  "http://localhost:8000/api/admin/analytics/system"

# Test session stats
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  "http://localhost:8000/api/admin/session-stats"
```

---

## 🌐 **Admin Interface Setup**

### **1. Serve Admin Files**
```bash
# Option 1: Simple Python server
cd /path/to/your/project
python -m http.server 8080

# Option 2: Use Node.js serve (if you have Node.js)
npx serve . -p 8080

# Option 3: Use your existing web server
# Copy admin HTML files to your web server directory
```

### **2. Configure Admin Interface**
Edit the admin JavaScript files to use your local API:

```javascript
// In admin_user_search.js and admin_dashboard.html
// Update the API base URL
const API_BASE_URL = 'http://localhost:8000';

// Update the auth token (for testing)
getAuthToken() {
    return 'your_admin_token_here'; // Replace with actual token
}
```

### **3. Test Admin Interface**
```bash
# Open admin dashboard
open http://localhost:8080/admin_dashboard.html

# Or manually navigate to:
# http://localhost:8080/admin_user_search.html
# http://localhost:8080/admin_user_profile.html
```

---

## 🐛 **Common Issues & Debugging**

### **Database Issues**
```bash
# Check if migration ran successfully
alembic current
alembic history

# If migration failed, check logs
alembic upgrade head --sql  # See SQL that would be executed

# Reset migration (if needed)
alembic downgrade base
alembic upgrade head
```

### **API Issues**
```bash
# Check FastAPI logs for errors
uvicorn api:app --reload --log-level debug

# Test individual endpoints
curl -v http://localhost:8000/health  # Basic health check
curl -v http://localhost:8000/stores  # Existing endpoint

# Check database connections
python -c "
from database import get_db
try:
    db = next(get_db())
    print('Database connection successful')
    db.close()
except Exception as e:
    print('Database error:', e)
"
```

### **Session Middleware Issues**
```bash
# Check if middleware is loaded
python -c "
from api import app
print('Middleware stack:')
for middleware in app.user_middleware:
    print(f'  {middleware.cls.__name__}')
"

# Test session creation manually
python -c "
from session_tracking_middleware import SessionTrackingMiddleware
middleware = SessionTrackingMiddleware(None)
session_id = middleware.generate_session_id()
print(f'Generated session ID: {session_id}')
"
```

### **Admin Interface Issues**
```bash
# Check browser console for JavaScript errors
# Open browser dev tools (F12) and check Console tab

# Test API calls manually
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  "http://localhost:8000/api/admin/users/search"

# Check CORS issues (if admin interface on different port)
# Add CORS middleware to api.py if needed
```

---

## 📊 **Generate Test Data**

### **Create Sample Users and Activity**
```python
# Run this script to generate test data
from database import get_db, User, UserQuery, UserSession, UserInteraction
from datetime import datetime, timedelta
import random

db = next(get_db())

# Create test users
test_users = []
for i in range(10):
    user = User(
        username=f"testuser{i}@example.com",
        hashed_password="dummy_hash",
        is_admin=False
    )
    db.add(user)
    test_users.append(user)

db.commit()

# Create test queries
sample_queries = ["pizza", "øl tilbud", "brød", "kød", "grøntsager"]
for user in test_users:
    for _ in range(random.randint(1, 10)):
        query = UserQuery(
            user_id=user.id,
            query_text=random.choice(sample_queries),
            selected_stores=[1, 2, 3],
            results_count=random.randint(0, 20),
            response_time_ms=random.randint(100, 1000),
            was_successful=random.choice([True, True, True, False]),  # 75% success
            query_category="food_beverages"
        )
        db.add(query)

# Create test sessions
for user in test_users:
    for _ in range(random.randint(1, 5)):
        session = UserSession(
            user_id=user.id,
            session_id=f"test_session_{user.id}_{random.randint(1000, 9999)}",
            session_start=datetime.utcnow() - timedelta(days=random.randint(0, 30)),
            duration_seconds=random.randint(60, 1800),
            queries_count=random.randint(1, 10),
            interactions_count=random.randint(0, 20),
            device_type=random.choice(["desktop", "mobile", "tablet"]),
            browser=random.choice(["chrome", "firefox", "safari"])
        )
        db.add(session)

db.commit()
print(f"Created {len(test_users)} test users with sample activity")
db.close()
```

---

## ✅ **Verification Checklist**

### **Backend Verification**
- [ ] Database migration completed successfully
- [ ] All 5 user activity tables created
- [ ] FastAPI server starts without errors
- [ ] Session middleware creates sessions
- [ ] Query logging works on /ask endpoint
- [ ] Interaction logging endpoints respond
- [ ] Admin API endpoints return data
- [ ] Admin authentication works

### **Frontend Verification**
- [ ] Admin dashboard loads without errors
- [ ] User search interface displays
- [ ] Charts render with sample data
- [ ] API calls succeed (check browser console)
- [ ] Pagination works with test data
- [ ] User profile modal opens
- [ ] Responsive design works on mobile

### **Integration Verification**
- [ ] User queries appear in admin search
- [ ] Session data shows in user profiles
- [ ] Interactions logged and displayed
- [ ] System analytics show correct metrics
- [ ] Real-time updates work
- [ ] Error handling works gracefully

---

## 🎯 **Ready for Production**

Once all verification steps pass, your local development environment is ready! The system provides:

✅ **Complete user activity tracking**
✅ **Professional admin interface**
✅ **Real-time analytics and insights**
✅ **GDPR-compliant audit logging**
✅ **Scalable architecture**

**Your local development environment is now ready for testing and debugging!** 🚀
