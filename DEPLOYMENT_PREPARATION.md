# Deployment Preparation Guide

## 🚀 **Production Deployment Checklist**

This guide prepares your user activity tracking system for production deployment on Render or your preferred hosting platform.

---

## 📋 **Pre-Deployment Checklist**

### **Code Quality & Security**
- [ ] All sensitive data moved to environment variables
- [ ] Debug mode disabled in production
- [ ] Strong admin passwords and JWT secrets
- [ ] HTTPS enforced for admin interface
- [ ] CORS configured for production domains
- [ ] Rate limiting implemented on admin endpoints
- [ ] Input validation on all endpoints
- [ ] SQL injection protection verified

### **Database Preparation**
- [ ] Production database backup created
- [ ] Migration tested on staging environment
- [ ] Database indexes optimized for performance
- [ ] Connection pooling configured
- [ ] Database monitoring set up

### **Performance Optimization**
- [ ] Static files optimized and compressed
- [ ] CDN configured for admin interface assets
- [ ] Database query performance tested
- [ ] Memory usage profiled and optimized
- [ ] Response time benchmarks established

---

## 🔧 **Environment Configuration**

### **Production Environment Variables**
```bash
# Database
DATABASE_URL=postgresql://user:password@host:port/database

# Security
ADMIN_JWT_SECRET=your_super_secure_production_secret_key_here
ADMIN_TOKEN_EXPIRE_HOURS=8
SECRET_KEY=your_fastapi_secret_key

# Session Management
SESSION_TIMEOUT_MINUTES=30
SESSION_CLEANUP_INTERVAL_HOURS=24

# Production Settings
ENVIRONMENT=production
DEBUG=False
ALLOWED_HOSTS=tilbudsjaegeren.dk,admin.tilbudsjaegeren.dk

# External Services
GOOGLE_CLOUD_PROJECT=your_project_id
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Monitoring
SENTRY_DSN=your_sentry_dsn_for_error_tracking
LOG_LEVEL=INFO
```

### **Render Configuration**
Create `render.yaml` in your project root:
```yaml
services:
  - type: web
    name: tilbudsjaegeren-api
    env: python
    buildCommand: |
      pip install -r requirements.txt
      alembic upgrade head
    startCommand: uvicorn api:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: tilbudsjaegeren-db
          property: connectionString
      - key: ADMIN_JWT_SECRET
        generateValue: true
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false

  - type: static
    name: tilbudsjaegeren-admin
    buildCommand: |
      # Copy admin files and update configuration
      mkdir -p dist
      cp admin_*.html admin_*.js dist/
      # Update API URLs for production
      sed -i 's|http://localhost:8000|https://tilbudsjaegeren-api.onrender.com|g' dist/*.js
    staticPublishPath: ./dist
    routes:
      - type: rewrite
        source: /admin/*
        destination: /admin/index.html

databases:
  - name: tilbudsjaegeren-db
    databaseName: tilbudsjaegeren
    user: tilbudsjaegeren_user
```

---

## 🗄️ **Database Migration Strategy**

### **Production Migration Plan**
```bash
# 1. Backup existing database
pg_dump $DATABASE_URL > backup_before_user_activity_$(date +%Y%m%d).sql

# 2. Test migration on staging
export DATABASE_URL="your_staging_database_url"
alembic upgrade head

# 3. Verify tables created
psql $DATABASE_URL -c "\dt user_*"

# 4. Run migration on production (during maintenance window)
export DATABASE_URL="your_production_database_url"
alembic upgrade head

# 5. Verify production migration
psql $DATABASE_URL -c "SELECT COUNT(*) FROM user_queries;"
```

### **Rollback Plan**
```bash
# If migration fails, rollback
alembic downgrade -1

# If data corruption, restore from backup
psql $DATABASE_URL < backup_before_user_activity_$(date +%Y%m%d).sql
```

---

## 🔐 **Security Hardening**

### **Admin Authentication**
```python
# Update auth.py for production
import os
from datetime import timedelta

# Use strong JWT secret
JWT_SECRET = os.getenv("ADMIN_JWT_SECRET")
if not JWT_SECRET or len(JWT_SECRET) < 32:
    raise ValueError("ADMIN_JWT_SECRET must be at least 32 characters")

# Shorter token expiry for production
ACCESS_TOKEN_EXPIRE_HOURS = int(os.getenv("ADMIN_TOKEN_EXPIRE_HOURS", "8"))

# Rate limiting for admin endpoints
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Apply rate limiting to admin endpoints
@app.get("/api/admin/users/search")
@limiter.limit("10/minute")  # Max 10 requests per minute
async def admin_search_users(request: Request, ...):
    # ... existing code
```

### **CORS Configuration**
```python
# Production CORS settings
from fastapi.middleware.cors import CORSMiddleware

if os.getenv("ENVIRONMENT") == "production":
    allowed_origins = [
        "https://tilbudsjaegeren.dk",
        "https://admin.tilbudsjaegeren.dk",
        "https://tilbudsjaegeren-admin.onrender.com"
    ]
else:
    allowed_origins = ["http://localhost:8080", "http://127.0.0.1:8080"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

---

## 📊 **Monitoring & Logging**

### **Error Tracking**
```python
# Add Sentry for error tracking
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

if os.getenv("SENTRY_DSN"):
    sentry_sdk.init(
        dsn=os.getenv("SENTRY_DSN"),
        integrations=[
            FastApiIntegration(auto_enabling_integrations=False),
            SqlalchemyIntegration(),
        ],
        traces_sample_rate=0.1,
        environment=os.getenv("ENVIRONMENT", "development")
    )
```

### **Structured Logging**
```python
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        return json.dumps(log_entry)

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(message)s",
    handlers=[logging.StreamHandler()]
)

if os.getenv("ENVIRONMENT") == "production":
    for handler in logging.root.handlers:
        handler.setFormatter(JSONFormatter())
```

### **Health Checks**
```python
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check database connection
        db = next(get_db())
        db.execute("SELECT 1")
        db.close()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "environment": os.getenv("ENVIRONMENT", "development")
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

@app.get("/metrics")
async def metrics():
    """Basic metrics for monitoring"""
    db = next(get_db())
    try:
        metrics = {
            "active_sessions": db.query(UserSession).filter(
                UserSession.session_end.is_(None)
            ).count(),
            "total_queries_today": db.query(UserQuery).filter(
                UserQuery.created_at >= datetime.utcnow().date()
            ).count(),
            "total_users": db.query(User).count(),
            "timestamp": datetime.utcnow().isoformat()
        }
        return metrics
    finally:
        db.close()
```

---

## 🌐 **Frontend Deployment**

### **Admin Interface Configuration**
```javascript
// Create production config file: admin_config.js
const ADMIN_CONFIG = {
    API_BASE_URL: window.location.hostname.includes('localhost') 
        ? 'http://localhost:8000'
        : 'https://tilbudsjaegeren-api.onrender.com',
    
    ENVIRONMENT: window.location.hostname.includes('localhost') 
        ? 'development' 
        : 'production',
    
    AUTH_TOKEN_KEY: 'tilbudsjaegeren_admin_token',
    SESSION_TIMEOUT_MINUTES: 30,
    AUTO_REFRESH_INTERVAL: 5 * 60 * 1000, // 5 minutes
    
    // Feature flags
    FEATURES: {
        AUTO_REFRESH: true,
        EXPORT_DATA: true,
        ADVANCED_ANALYTICS: true
    }
};

// Update admin JavaScript files to use config
// In admin_user_search.js:
getAuthToken() {
    return localStorage.getItem(ADMIN_CONFIG.AUTH_TOKEN_KEY) || 
           this.getCookie(ADMIN_CONFIG.AUTH_TOKEN_KEY);
}

async apiCall(endpoint, options = {}) {
    const url = `${ADMIN_CONFIG.API_BASE_URL}${endpoint}`;
    // ... rest of method
}
```

### **Build Process**
```bash
#!/bin/bash
# build_admin.sh - Build script for admin interface

echo "Building admin interface for production..."

# Create build directory
mkdir -p dist/admin

# Copy HTML files and update API URLs
for file in admin_*.html; do
    sed "s|http://localhost:8000|https://tilbudsjaegeren-api.onrender.com|g" "$file" > "dist/admin/$file"
done

# Copy JavaScript files and update configuration
for file in admin_*.js; do
    sed "s|http://localhost:8000|https://tilbudsjaegeren-api.onrender.com|g" "$file" > "dist/admin/$file"
done

# Copy CSS and other assets
cp -r admin_assets/* dist/admin/ 2>/dev/null || true

# Minify files (if you have minification tools)
# uglifyjs dist/admin/*.js -o dist/admin/admin.min.js
# cleancss dist/admin/*.css -o dist/admin/admin.min.css

echo "Admin interface built successfully in dist/admin/"
```

---

## 📈 **Performance Optimization**

### **Database Optimization**
```sql
-- Create additional indexes for performance
CREATE INDEX CONCURRENTLY idx_user_queries_user_created 
ON user_queries(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_user_sessions_user_start 
ON user_sessions(user_id, session_start DESC);

CREATE INDEX CONCURRENTLY idx_user_interactions_user_created 
ON user_interactions(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_user_interactions_type_created 
ON user_interactions(interaction_type, created_at DESC);

-- Analyze tables for query optimization
ANALYZE user_queries;
ANALYZE user_sessions;
ANALYZE user_interactions;
```

### **Connection Pooling**
```python
# Update database.py for production
from sqlalchemy.pool import QueuePool

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=False  # Disable SQL logging in production
)
```

---

## 🧪 **Pre-Deployment Testing**

### **Load Testing**
```python
# load_test.py - Basic load testing script
import asyncio
import aiohttp
import time

async def test_endpoint(session, url, headers=None):
    try:
        async with session.get(url, headers=headers) as response:
            return response.status, await response.text()
    except Exception as e:
        return 500, str(e)

async def load_test():
    headers = {"Authorization": "Bearer YOUR_ADMIN_TOKEN"}
    urls = [
        "http://localhost:8000/api/admin/session-stats",
        "http://localhost:8000/api/admin/users/search?limit=10",
        "http://localhost:8000/api/admin/analytics/system"
    ]
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        for _ in range(50):  # 50 concurrent requests
            for url in urls:
                tasks.append(test_endpoint(session, url, headers))
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        success_count = sum(1 for status, _ in results if status == 200)
        total_requests = len(results)
        
        print(f"Load test results:")
        print(f"Total requests: {total_requests}")
        print(f"Successful requests: {success_count}")
        print(f"Success rate: {success_count/total_requests*100:.1f}%")
        print(f"Total time: {end_time-start_time:.2f}s")
        print(f"Requests per second: {total_requests/(end_time-start_time):.1f}")

if __name__ == "__main__":
    asyncio.run(load_test())
```

### **Integration Testing**
```bash
# integration_test.sh - Test complete user journey
#!/bin/bash

echo "Running integration tests..."

# Test 1: User makes query
echo "Test 1: User query logging"
curl -X POST http://localhost:8000/ask \
  -H "Content-Type: application/json" \
  -d '{"query": "integration test pizza", "selected_stores": [1, 2, 3]}'

# Test 2: Admin searches for user
echo "Test 2: Admin user search"
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  "http://localhost:8000/api/admin/users/search?search_term=integration"

# Test 3: Admin views analytics
echo "Test 3: Admin analytics"
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  "http://localhost:8000/api/admin/analytics/system"

echo "Integration tests completed"
```

---

## ✅ **Deployment Checklist**

### **Final Pre-Deployment Steps**
- [ ] All environment variables configured
- [ ] Database migration tested on staging
- [ ] Admin user created with strong password
- [ ] SSL certificates configured
- [ ] Monitoring and alerting set up
- [ ] Backup strategy implemented
- [ ] Load testing completed
- [ ] Security audit performed
- [ ] Documentation updated
- [ ] Team trained on admin interface

### **Post-Deployment Verification**
- [ ] Health check endpoint responds
- [ ] Admin interface loads without errors
- [ ] User activity logging works
- [ ] Session tracking active
- [ ] Admin API endpoints functional
- [ ] Charts and analytics display data
- [ ] Error tracking operational
- [ ] Performance metrics within targets

**Your user activity tracking system is now ready for production deployment!** 🚀

The system provides enterprise-grade user analytics, comprehensive admin capabilities, and scalable architecture that will grow with your business.
