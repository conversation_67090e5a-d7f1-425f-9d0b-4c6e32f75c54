"use client";

import { useState } from 'react';
import { 
  MagnifyingGlassIcon, 
  ShoppingCartIcon, 
  HeartIcon, 
  UserIcon 
} from '@heroicons/react/24/outline';
import { 
  MagnifyingGlassIcon as MagnifyingGlassIconSolid, 
  ShoppingCartIcon as ShoppingCartIconSolid, 
  HeartIcon as HeartIconSolid, 
  UserIcon as UserIconSolid 
} from '@heroicons/react/24/solid';

export type TabType = 'search' | 'shopping' | 'favorites' | 'profile';

interface BottomTabNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  shoppingListCount?: number;
  favoritesCount?: number;
}

export default function BottomTabNavigation({ 
  activeTab, 
  onTabChange, 
  shoppingListCount = 0,
  favoritesCount = 0 
}: BottomTabNavigationProps) {
  const tabs = [
    {
      id: 'search' as TabType,
      label: 'Søg',
      icon: MagnifyingGlassIcon,
      iconSolid: MagnifyingGlassIconSolid,
      badge: null
    },
    {
      id: 'shopping' as TabType,
      label: 'Indkøb',
      icon: ShoppingCartIcon,
      iconSolid: ShoppingCartIconSolid,
      badge: shoppingListCount > 0 ? shoppingListCount : null
    },
    {
      id: 'favorites' as TabType,
      label: 'Favoritter',
      icon: HeartIcon,
      iconSolid: HeartIconSolid,
      badge: favoritesCount > 0 ? favoritesCount : null
    },
    {
      id: 'profile' as TabType,
      label: 'Profil',
      icon: UserIcon,
      iconSolid: UserIconSolid,
      badge: null
    }
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 safe-area-pb z-50">
      <div className="flex items-center justify-around px-2 py-1">
        {tabs.map((tab) => {
          const isActive = activeTab === tab.id;
          const Icon = isActive ? tab.iconSolid : tab.icon;
          
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                relative flex flex-col items-center justify-center px-3 py-2 min-w-0 flex-1
                transition-all duration-200 ease-out
                ${isActive 
                  ? 'text-blue-600 scale-105' 
                  : 'text-gray-500 hover:text-gray-700 active:scale-95'
                }
              `}
            >
              {/* Icon with badge */}
              <div className="relative">
                <Icon className="w-6 h-6 mb-1" />
                {tab.badge && (
                  <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </div>
                )}
              </div>
              
              {/* Label */}
              <span className={`
                text-xs font-medium truncate w-full text-center
                ${isActive ? 'text-blue-600' : 'text-gray-500'}
              `}>
                {tab.label}
              </span>
              
              {/* Active indicator */}
              {isActive && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full" />
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
}
