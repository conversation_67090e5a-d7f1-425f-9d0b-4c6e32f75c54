"use client";

import { useState, useEffect } from 'react';
import { ShoppingCartIcon, TrashIcon, ShareIcon } from '@heroicons/react/24/outline';

interface ShoppingTabProps {
  onCountChange: (count: number) => void;
}

interface ShoppingItem {
  id: string;
  name: string;
  price: string;
  store: string;
  quantity: number;
}

export default function ShoppingTab({ onCountChange }: ShoppingTabProps) {
  const [activeView, setActiveView] = useState<'store' | 'meal'>('store');
  const [shoppingList, setShoppingList] = useState<ShoppingItem[]>([
    // Mock data for demonstration
    {
      id: '1',
      name: 'Arla Økologisk Mælk',
      price: '12,95 kr',
      store: 'Netto',
      quantity: 1
    },
    {
      id: '2',
      name: '<PERSON><PERSON>ø<PERSON>',
      price: '8,50 kr',
      store: 'Netto',
      quantity: 2
    },
    {
      id: '3',
      name: '<PERSON><PERSON>',
      price: '45,00 kr',
      store: 'Lidl',
      quantity: 1
    }
  ]);

  // Update count when list changes
  useEffect(() => {
    onCountChange(shoppingList.length);
  }, [shoppingList, onCountChange]);

  const removeItem = (id: string) => {
    setShoppingList(prev => prev.filter(item => item.id !== id));
  };

  const clearList = () => {
    setShoppingList([]);
  };

  const groupByStore = () => {
    const grouped: { [store: string]: ShoppingItem[] } = {};
    shoppingList.forEach(item => {
      if (!grouped[item.store]) {
        grouped[item.store] = [];
      }
      grouped[item.store].push(item);
    });
    return grouped;
  };

  const calculateStoreTotal = (items: ShoppingItem[]) => {
    return items.reduce((total, item) => {
      const price = parseFloat(item.price.replace(',', '.').replace(' kr', ''));
      return total + (price * item.quantity);
    }, 0);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white px-4 pt-16 pb-6 shadow-sm">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <ShoppingCartIcon className="w-6 h-6 text-blue-600" />
              <h1 className="text-xl font-semibold text-gray-900">Indkøbsliste</h1>
              {shoppingList.length > 0 && (
                <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2 py-1 rounded-full">
                  {shoppingList.length}
                </span>
              )}
            </div>
            
            {shoppingList.length > 0 && (
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    // TODO: Implement share functionality
                    console.log('Share shopping list');
                  }}
                  className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                >
                  <ShareIcon className="w-5 h-5" />
                </button>
                <button
                  onClick={clearList}
                  className="p-2 text-gray-600 hover:text-red-600 transition-colors"
                >
                  <TrashIcon className="w-5 h-5" />
                </button>
              </div>
            )}
          </div>

          {/* View Toggle */}
          {shoppingList.length > 0 && (
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveView('store')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeView === 'store'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Efter butik
              </button>
              <button
                onClick={() => setActiveView('meal')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeView === 'meal'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Efter måltid
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6">
        {shoppingList.length === 0 ? (
          /* Empty State */
          <div className="max-w-2xl mx-auto text-center py-12">
            <ShoppingCartIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Din indkøbsliste er tom</h3>
            <p className="text-gray-600 mb-6">
              Produkter du tilføjer fra søgeresultater vil vises her
            </p>
            <button
              onClick={() => {
                // TODO: Switch to search tab
                console.log('Switch to search tab');
              }}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Start med at søge
            </button>
          </div>
        ) : (
          /* Shopping List Content */
          <div className="max-w-2xl mx-auto space-y-4">
            {activeView === 'store' ? (
              /* Group by Store */
              Object.entries(groupByStore()).map(([store, items]) => (
                <div key={store} className="bg-white rounded-lg shadow-sm overflow-hidden">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">📍 {store}</h3>
                      <span className="text-sm text-gray-600">
                        {calculateStoreTotal(items).toFixed(2).replace('.', ',')} kr
                      </span>
                    </div>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {items.map((item) => (
                      <div key={item.id} className="px-4 py-3 flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{item.name}</h4>
                          <p className="text-sm text-gray-600">
                            {item.quantity}x • {item.price}
                          </p>
                        </div>
                        <button
                          onClick={() => removeItem(item.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            ) : (
              /* Group by Meal - Placeholder */
              <div className="bg-white rounded-lg shadow-sm p-6 text-center">
                <h3 className="font-medium text-gray-900 mb-2">Måltidsplanlægning</h3>
                <p className="text-gray-600 text-sm">
                  Denne funktion kommer snart! Du vil kunne organisere dine indkøb efter måltider.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
