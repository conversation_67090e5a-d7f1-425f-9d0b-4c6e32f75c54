# Admin API Endpoints Documentation

## 🎯 **Admin API Endpoints for User Data Retrieval**

### **Authentication Required**
All admin endpoints require admin authentication via `Depends(auth.require_admin)`.

---

## **👥 User Management Endpoints**

### **GET /api/admin/users/search**
Search users with filters and pagination.

**Query Parameters:**
- `search_term` (optional): Search term for username
- `user_type` (optional): Filter by user type (`active`, `inactive`, `power_user`)
- `date_from` (optional): Date from (ISO format)
- `date_to` (optional): Date to (ISO format)
- `limit` (default: 50): Number of results per page
- `offset` (default: 0): Offset for pagination

**Response:**
```json
{
  "users": [
    {
      "id": 123,
      "username": "<EMAIL>",
      "is_admin": false,
      "created_at": "2025-01-15T10:30:00",
      "total_queries": 45,
      "total_sessions": 12,
      "last_activity": "2025-01-20T14:22:00",
      "avg_session_duration": 180,
      "user_type": "regular_user"
    }
  ],
  "total_count": 150,
  "limit": 50,
  "offset": 0,
  "has_more": true
}
```

**Example:**
```bash
GET /api/admin/users/search?search_term=john&user_type=active&limit=20
```

---

### **GET /api/admin/users/{user_id}/profile**
Get comprehensive user profile with activity summary.

**Path Parameters:**
- `user_id`: User ID to retrieve profile for

**Response:**
```json
{
  "id": 123,
  "username": "<EMAIL>",
  "is_admin": false,
  "created_at": "2025-01-15T10:30:00",
  "first_activity": "2025-01-15T11:00:00",
  "last_activity": "2025-01-20T14:22:00",
  "stats": {
    "total_queries": 45,
    "total_sessions": 12,
    "total_interactions": 89,
    "avg_session_duration": 180,
    "total_session_queries": 52
  },
  "query_categories": {
    "food_beverages": 25,
    "household_cleaning": 15,
    "electronics": 5
  },
  "interaction_types": {
    "product_clicked": 35,
    "catalog_viewed": 20,
    "store_selected": 15
  },
  "recent_queries": [
    {
      "id": 456,
      "query_text": "pizza tilbud",
      "created_at": "2025-01-20T14:22:00",
      "was_successful": true,
      "results_count": 8
    }
  ],
  "recent_interactions": [
    {
      "id": 789,
      "interaction_type": "product_clicked",
      "target_type": "product",
      "target_id": 123,
      "created_at": "2025-01-20T14:25:00"
    }
  ],
  "user_type": "regular_user"
}
```

---

### **GET /api/admin/users/{user_id}/timeline**
Get user's activity timeline with chronological activities.

**Path Parameters:**
- `user_id`: User ID to retrieve timeline for

**Query Parameters:**
- `activity_type` (optional): Filter by activity type (`queries`, `interactions`, `sessions`)
- `date_from` (optional): Date from (ISO format)
- `date_to` (optional): Date to (ISO format)
- `limit` (default: 100): Number of activities to return
- `offset` (default: 0): Offset for pagination

**Response:**
```json
{
  "user_id": 123,
  "timeline": [
    {
      "type": "query",
      "id": 456,
      "timestamp": "2025-01-20T14:22:00",
      "data": {
        "query_text": "pizza tilbud",
        "selected_stores": [1, 2, 3],
        "results_count": 8,
        "was_successful": true,
        "response_time_ms": 250,
        "query_category": "food_beverages"
      }
    },
    {
      "type": "interaction",
      "id": 789,
      "timestamp": "2025-01-20T14:25:00",
      "data": {
        "interaction_type": "product_clicked",
        "target_type": "product",
        "target_id": 123,
        "source_query_id": 456,
        "interaction_data": {
          "product_name": "Pizza Margherita",
          "position_in_results": 2
        }
      }
    }
  ],
  "activity_type": null,
  "limit": 100,
  "offset": 0
}
```

---

### **GET /api/admin/users/{user_id}/queries**
Get user's query history with detailed information.

**Path Parameters:**
- `user_id`: User ID to retrieve queries for

**Query Parameters:**
- `date_from` (optional): Date from (ISO format)
- `date_to` (optional): Date to (ISO format)
- `limit` (default: 100): Number of queries to return
- `offset` (default: 0): Offset for pagination

**Response:**
```json
{
  "user_id": 123,
  "queries": [
    {
      "id": 456,
      "query_text": "pizza tilbud",
      "selected_stores": [1, 2, 3],
      "query_model": "gemini-2.5-flash-lite",
      "results_count": 8,
      "response_time_ms": 250,
      "was_successful": true,
      "error_message": null,
      "query_category": "food_beverages",
      "query_intent": "price_comparison",
      "created_at": "2025-01-20T14:22:00"
    }
  ],
  "total_count": 45,
  "limit": 100,
  "offset": 0,
  "has_more": false
}
```

---

## **📊 Analytics Endpoints**

### **GET /api/admin/analytics/system**
Get system-wide analytics and metrics.

**Query Parameters:**
- `date_from` (optional): Date from (ISO format, default: 30 days ago)
- `date_to` (optional): Date to (ISO format, default: now)

**Response:**
```json
{
  "date_range": {
    "from": "2024-12-21T00:00:00",
    "to": "2025-01-20T23:59:59"
  },
  "users": {
    "total": 1250,
    "active": 450,
    "activity_rate": 36.0
  },
  "queries": {
    "total": 5680,
    "successful": 5234,
    "success_rate": 92.1,
    "avg_response_time_ms": 285
  },
  "sessions": {
    "total": 2340,
    "avg_duration_seconds": 195
  },
  "interactions": {
    "total": 8920
  },
  "popular_queries": [
    {"query": "pizza", "count": 156},
    {"query": "øl tilbud", "count": 134},
    {"query": "brød", "count": 98}
  ],
  "category_breakdown": [
    {"category": "food_beverages", "count": 2340},
    {"category": "household_cleaning", "count": 890},
    {"category": "electronics", "count": 456}
  ]
}
```

---

### **GET /api/admin/analytics/queries**
Get detailed query analytics and patterns.

**Query Parameters:**
- `date_from` (optional): Date from (ISO format, default: 30 days ago)
- `date_to` (optional): Date to (ISO format, default: now)
- `limit` (default: 50): Number of results to return

**Response:**
```json
{
  "popular_queries": [
    {
      "query": "pizza tilbud",
      "frequency": 156,
      "avg_response_time_ms": 245,
      "avg_results": 12,
      "success_rate": 94.2
    }
  ],
  "failed_queries": [
    {
      "query": "invalid search term",
      "error_message": "No products found",
      "failure_count": 23
    }
  ],
  "refinement_count": 89
}
```

---

## **🔐 Admin Session Monitoring**

### **GET /api/admin/session-stats**
Get current session statistics for monitoring.

**Response:**
```json
{
  "total_sessions": 150,
  "active_sessions": 45,
  "total_queries": 1250,
  "total_interactions": 890,
  "last_cleanup": "2025-01-20T12:00:00"
}
```

---

## **🔍 Usage Examples**

### **Search for Power Users**
```bash
curl -H "Authorization: Bearer <admin_token>" \
  "https://api.tilbudsjaegeren.dk/api/admin/users/search?user_type=power_user&limit=20"
```

### **Get User Profile**
```bash
curl -H "Authorization: Bearer <admin_token>" \
  "https://api.tilbudsjaegeren.dk/api/admin/users/123/profile"
```

### **Get User Activity Timeline**
```bash
curl -H "Authorization: Bearer <admin_token>" \
  "https://api.tilbudsjaegeren.dk/api/admin/users/123/timeline?activity_type=queries&limit=50"
```

### **Get System Analytics for Last Week**
```bash
curl -H "Authorization: Bearer <admin_token>" \
  "https://api.tilbudsjaegeren.dk/api/admin/analytics/system?date_from=2025-01-13T00:00:00Z&date_to=2025-01-20T23:59:59Z"
```

### **Get Query Analytics**
```bash
curl -H "Authorization: Bearer <admin_token>" \
  "https://api.tilbudsjaegeren.dk/api/admin/analytics/queries?limit=100"
```

---

## **📝 Admin Audit Logging**

All admin endpoints automatically log access for GDPR compliance:

- **user_searched** - When admin searches for users
- **user_viewed** - When admin views user profile
- **user_timeline_viewed** - When admin views user timeline
- **user_queries_viewed** - When admin views user queries
- **system_analytics_viewed** - When admin views system analytics
- **query_analytics_viewed** - When admin views query analytics

Audit logs include:
- Admin user ID
- Target user ID (if applicable)
- Resource accessed
- Action details (search terms, result counts, etc.)
- Timestamp and IP address

---

## **🚀 Integration Ready**

These admin API endpoints provide comprehensive access to user data and analytics, enabling:

- **User Support** - Deep-dive into specific user issues
- **Business Intelligence** - Understand usage patterns and trends
- **Performance Monitoring** - Track system health and user experience
- **Compliance** - GDPR-ready audit trails and data access logging

**Ready for admin panel frontend integration!** 🎯
