@import 'tailwindcss';

@plugin "@tailwindcss/container-queries";
@plugin "@tailwindcss/typography";

/* Import flipbook viewer styles */
@import '../styles/flipbook.css';

/* Custom animations for hybrid designs */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-down {
  animation: slide-down 0.4s ease-out;
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

@supports (font: -apple-system-body) and (-webkit-appearance: none) {
  img[loading='lazy'] {
    clip-path: inset(0.6px);
  }
}

a,
input,
button {
  @apply focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-neutral-400 focus-visible:ring-offset-2 focus-visible:ring-offset-neutral-50 dark:focus-visible:ring-neutral-600 dark:focus-visible:ring-offset-neutral-900;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile-first native app styles */
@layer utilities {
  /* Safe area support for mobile devices */
  .safe-area-pt {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-pl {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-pr {
    padding-right: env(safe-area-inset-right);
  }

  /* Native app scrolling */
  .native-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Smooth transitions for native feel */
  .native-transition {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Touch-friendly tap targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Prevent text selection on UI elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Active state for touch devices */
  .touch-active:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}

/* Enhanced global styles for native app feel */
html {
  /* Prevent zoom on iOS */
  -webkit-text-size-adjust: 100%;
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

body {
  /* Prevent overscroll bounce */
  overscroll-behavior: none;
  /* Native font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
