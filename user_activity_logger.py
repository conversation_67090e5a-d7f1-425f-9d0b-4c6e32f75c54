"""
User Activity Logging Service

Centralized service for logging all user activities including queries, sessions,
interactions, and preference changes. Handles both authenticated and anonymous users.
"""

import uuid
import time
import json
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, List, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from database import (
    UserQuery, UserSession, UserInteraction, 
    UserPreferencesHistory, AdminAuditLog, get_db
)
from user_activity_constants import (
    categorize_query, detect_query_intent, get_device_type_from_user_agent,
    is_valid_interaction_type, is_valid_query_category, is_valid_preference_type
)

# Set up logging
logger = logging.getLogger(__name__)

class UserActivityLogger:
    """
    Centralized logging service for all user activities.
    Handles both authenticated and anonymous users with proper error handling.
    """
    
    def __init__(self, db_session: Optional[Session] = None):
        """
        Initialize the logger with optional database session.
        If no session provided, will create one per operation.
        """
        self.db_session = db_session
        self._should_close_session = db_session is None
    
    def _get_db_session(self) -> Session:
        """Get database session, creating one if needed."""
        if self.db_session:
            return self.db_session
        return next(get_db())
    
    def _close_db_session(self, db: Session):
        """Close database session if we created it."""
        if self._should_close_session:
            db.close()
    
    def _safe_execute(self, operation_name: str, operation_func, *args, **kwargs):
        """
        Safely execute database operations with error handling.
        Returns (success: bool, result: Any, error: str)
        """
        db = None
        try:
            db = self._get_db_session()
            result = operation_func(db, *args, **kwargs)
            if self._should_close_session:
                db.commit()
            return True, result, None
        except SQLAlchemyError as e:
            logger.error(f"Database error in {operation_name}: {str(e)}")
            if db and self._should_close_session:
                db.rollback()
            return False, None, str(e)
        except Exception as e:
            logger.error(f"Unexpected error in {operation_name}: {str(e)}")
            if db and self._should_close_session:
                db.rollback()
            return False, None, str(e)
        finally:
            if db:
                self._close_db_session(db)
    
    # --- Query Logging ---
    
    def log_query(
        self,
        query_text: str,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        selected_stores: Optional[List[int]] = None,
        query_model: Optional[str] = None,
        results_count: Optional[int] = None,
        response_time_ms: Optional[int] = None,
        was_successful: bool = True,
        error_message: Optional[str] = None,
        user_locale: str = 'da-DK',
        ip_country: Optional[str] = None,
        timezone_str: Optional[str] = None,
        auto_categorize: bool = True
    ) -> Optional[int]:
        """
        Log a user query with full context.
        
        Args:
            query_text: The search query text
            user_id: User ID if authenticated (None for anonymous)
            session_id: Session identifier (generated if None)
            selected_stores: List of store IDs selected for search
            query_model: AI model used for the query
            results_count: Number of results returned
            response_time_ms: Query response time in milliseconds
            was_successful: Whether the query succeeded
            error_message: Error message if query failed
            user_locale: User's locale (e.g., 'da-DK')
            ip_country: Country code from IP address
            timezone_str: User's timezone
            auto_categorize: Whether to auto-categorize the query
            
        Returns:
            Query ID if successful, None if failed
        """
        def _log_query_operation(db: Session) -> int:
            # Generate session ID if not provided
            if not session_id:
                session_id_to_use = f"anon_{uuid.uuid4().hex[:12]}"
            else:
                session_id_to_use = session_id
            
            # Auto-categorize query if enabled
            query_category = None
            query_intent = None
            if auto_categorize:
                query_category = categorize_query(query_text)
                query_intent = detect_query_intent(query_text, len(selected_stores or []))
            
            # Create query log entry
            query_log = UserQuery(
                user_id=user_id,
                session_id=session_id_to_use,
                query_text=query_text,
                selected_stores=selected_stores,
                query_model=query_model,
                results_count=results_count,
                response_time_ms=response_time_ms,
                was_successful=was_successful,
                error_message=error_message,
                query_category=query_category,
                query_intent=query_intent,
                detected_language='da',  # TODO: Add language detection
                user_locale=user_locale,
                ip_country=ip_country,
                timezone=timezone_str,
                created_at=datetime.utcnow()
            )
            
            db.add(query_log)
            db.flush()  # Get the ID without committing
            return query_log.id
        
        success, query_id, error = self._safe_execute("log_query", _log_query_operation)
        if not success:
            logger.warning(f"Failed to log query '{query_text[:50]}...': {error}")
        return query_id
    
    # --- Session Logging ---
    
    def start_session(
        self,
        session_id: str,
        user_id: Optional[int] = None,
        device_type: Optional[str] = None,
        browser: Optional[str] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        is_return_session: bool = False
    ) -> bool:
        """
        Start tracking a new user session.
        
        Args:
            session_id: Unique session identifier
            user_id: User ID if authenticated
            device_type: Device type ('mobile', 'desktop', 'tablet')
            browser: Browser name
            user_agent: Full user agent string
            ip_address: User's IP address
            is_return_session: Whether this is a returning user
            
        Returns:
            True if successful, False otherwise
        """
        def _start_session_operation(db: Session) -> bool:
            # Auto-detect device type if not provided
            if not device_type and user_agent:
                device_type_detected = get_device_type_from_user_agent(user_agent)
            else:
                device_type_detected = device_type or 'unknown'
            
            session_log = UserSession(
                user_id=user_id,
                session_id=session_id,
                session_start=datetime.utcnow(),
                device_type=device_type_detected,
                browser=browser,
                user_agent=user_agent,
                ip_address=ip_address,
                return_session=is_return_session,
                created_at=datetime.utcnow()
            )
            
            db.add(session_log)
            return True
        
        success, _, error = self._safe_execute("start_session", _start_session_operation)
        if not success:
            logger.warning(f"Failed to start session {session_id}: {error}")
        return success
    
    def end_session(
        self,
        session_id: str,
        queries_count: int = 0,
        interactions_count: int = 0,
        pages_viewed: int = 0
    ) -> bool:
        """
        End a user session and calculate metrics.
        
        Args:
            session_id: Session identifier to end
            queries_count: Total queries in this session
            interactions_count: Total interactions in this session
            pages_viewed: Total pages viewed in this session
            
        Returns:
            True if successful, False otherwise
        """
        def _end_session_operation(db: Session) -> bool:
            session = db.query(UserSession).filter(
                UserSession.session_id == session_id
            ).first()
            
            if not session:
                logger.warning(f"Session {session_id} not found for ending")
                return False
            
            # Calculate session duration
            session_end_time = datetime.utcnow()
            duration_seconds = int((session_end_time - session.session_start).total_seconds())
            
            # Determine if it was a bounce session (single query, short duration)
            is_bounce = queries_count <= 1 and duration_seconds < 30
            
            # Update session
            session.session_end = session_end_time
            session.duration_seconds = duration_seconds
            session.queries_count = queries_count
            session.interactions_count = interactions_count
            session.pages_viewed = pages_viewed
            session.bounce_session = is_bounce
            session.updated_at = datetime.utcnow()
            
            return True
        
        success, _, error = self._safe_execute("end_session", _end_session_operation)
        if not success:
            logger.warning(f"Failed to end session {session_id}: {error}")
        return success
    
    # --- Interaction Logging ---
    
    def log_interaction(
        self,
        interaction_type: str,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        target_type: Optional[str] = None,
        target_id: Optional[int] = None,
        source_query_id: Optional[int] = None,
        page_url: Optional[str] = None,
        referrer_url: Optional[str] = None,
        interaction_data: Optional[Dict[str, Any]] = None
    ) -> Optional[int]:
        """
        Log a user interaction (click, view, etc.).
        
        Args:
            interaction_type: Type of interaction (see INTERACTION_TYPES)
            user_id: User ID if authenticated
            session_id: Session identifier
            target_type: Type of target ('catalog', 'product', 'store')
            target_id: ID of the target
            source_query_id: Query that led to this interaction
            page_url: URL where interaction occurred
            referrer_url: Previous page URL
            interaction_data: Additional interaction data (JSON)
            
        Returns:
            Interaction ID if successful, None if failed
        """
        def _log_interaction_operation(db: Session) -> int:
            # Validate interaction type
            if not is_valid_interaction_type(interaction_type):
                logger.warning(f"Invalid interaction type: {interaction_type}")
                # Don't fail, just log the warning
            
            interaction_log = UserInteraction(
                user_id=user_id,
                session_id=session_id or f"anon_{uuid.uuid4().hex[:12]}",
                interaction_type=interaction_type,
                target_type=target_type,
                target_id=target_id,
                source_query_id=source_query_id,
                page_url=page_url,
                referrer_url=referrer_url,
                interaction_data=interaction_data,
                created_at=datetime.utcnow()
            )
            
            db.add(interaction_log)
            db.flush()
            return interaction_log.id
        
        success, interaction_id, error = self._safe_execute("log_interaction", _log_interaction_operation)
        if not success:
            logger.warning(f"Failed to log interaction {interaction_type}: {error}")
        return interaction_id

    # --- Preference Change Logging ---

    def log_preference_change(
        self,
        user_id: int,
        preference_type: str,
        old_value: Optional[str] = None,
        new_value: Optional[str] = None,
        change_reason: str = 'user_selection',
        admin_user_id: Optional[int] = None
    ) -> bool:
        """
        Log a change to user preferences.

        Args:
            user_id: User whose preferences changed
            preference_type: Type of preference (see PREFERENCE_TYPES)
            old_value: Previous value (JSON string or plain text)
            new_value: New value (JSON string or plain text)
            change_reason: Reason for change ('user_selection', 'admin_update', etc.)
            admin_user_id: Admin who made the change (if applicable)

        Returns:
            True if successful, False otherwise
        """
        def _log_preference_change_operation(db: Session) -> bool:
            # Validate preference type
            if not is_valid_preference_type(preference_type):
                logger.warning(f"Invalid preference type: {preference_type}")

            preference_log = UserPreferencesHistory(
                user_id=user_id,
                preference_type=preference_type,
                old_value=old_value,
                new_value=new_value,
                change_reason=change_reason,
                changed_by_admin=admin_user_id is not None,
                admin_user_id=admin_user_id,
                created_at=datetime.utcnow()
            )

            db.add(preference_log)
            return True

        success, _, error = self._safe_execute("log_preference_change", _log_preference_change_operation)
        if not success:
            logger.warning(f"Failed to log preference change for user {user_id}: {error}")
        return success

    # --- Admin Audit Logging ---

    def log_admin_action(
        self,
        admin_user_id: int,
        action_type: str,
        target_user_id: Optional[int] = None,
        resource_accessed: Optional[str] = None,
        action_details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        Log admin access to user data for GDPR compliance.

        Args:
            admin_user_id: Admin user performing the action
            action_type: Type of action (see ADMIN_AUDIT_ACTIONS)
            target_user_id: User whose data was accessed (if applicable)
            resource_accessed: Specific resource accessed
            action_details: Additional details about the action
            ip_address: Admin's IP address
            user_agent: Admin's user agent

        Returns:
            True if successful, False otherwise
        """
        def _log_admin_action_operation(db: Session) -> bool:
            audit_log = AdminAuditLog(
                admin_user_id=admin_user_id,
                target_user_id=target_user_id,
                action_type=action_type,
                resource_accessed=resource_accessed,
                action_details=action_details,
                ip_address=ip_address,
                user_agent=user_agent,
                created_at=datetime.utcnow()
            )

            db.add(audit_log)
            return True

        success, _, error = self._safe_execute("log_admin_action", _log_admin_action_operation)
        if not success:
            logger.warning(f"Failed to log admin action {action_type}: {error}")
        return success

    # --- Convenience Methods ---

    def log_successful_query(
        self,
        query_text: str,
        results_count: int,
        response_time_ms: int,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        selected_stores: Optional[List[int]] = None,
        query_model: str = 'gemini-2.5-flash-lite'
    ) -> Optional[int]:
        """Convenience method for logging successful queries."""
        return self.log_query(
            query_text=query_text,
            user_id=user_id,
            session_id=session_id,
            selected_stores=selected_stores,
            query_model=query_model,
            results_count=results_count,
            response_time_ms=response_time_ms,
            was_successful=True
        )

    def log_failed_query(
        self,
        query_text: str,
        error_message: str,
        response_time_ms: int,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        selected_stores: Optional[List[int]] = None,
        query_model: str = 'gemini-2.5-flash-lite'
    ) -> Optional[int]:
        """Convenience method for logging failed queries."""
        return self.log_query(
            query_text=query_text,
            user_id=user_id,
            session_id=session_id,
            selected_stores=selected_stores,
            query_model=query_model,
            results_count=0,
            response_time_ms=response_time_ms,
            was_successful=False,
            error_message=error_message
        )

    def log_catalog_view(
        self,
        catalog_id: int,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        source_query_id: Optional[int] = None,
        view_duration_seconds: Optional[int] = None
    ) -> Optional[int]:
        """Convenience method for logging catalog views."""
        interaction_data = {}
        if view_duration_seconds:
            interaction_data['duration_seconds'] = view_duration_seconds

        return self.log_interaction(
            interaction_type='catalog_viewed',
            user_id=user_id,
            session_id=session_id,
            target_type='catalog',
            target_id=catalog_id,
            source_query_id=source_query_id,
            interaction_data=interaction_data if interaction_data else None
        )

    def log_product_click(
        self,
        product_id: int,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        source_query_id: Optional[int] = None
    ) -> Optional[int]:
        """Convenience method for logging product clicks."""
        return self.log_interaction(
            interaction_type='product_clicked',
            user_id=user_id,
            session_id=session_id,
            target_type='product',
            target_id=product_id,
            source_query_id=source_query_id
        )

    def log_store_selection(
        self,
        store_ids: List[int],
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Convenience method for logging store selection changes."""
        return self.log_interaction(
            interaction_type='store_selected',
            user_id=user_id,
            session_id=session_id,
            target_type='store',
            interaction_data={'selected_stores': store_ids}
        )


# --- Global Logger Instance ---

# Create a global logger instance for easy use throughout the application
activity_logger = UserActivityLogger()

# --- Helper Functions for Easy Integration ---

def log_query_activity(
    query_text: str,
    results_count: int,
    response_time_ms: int,
    was_successful: bool = True,
    error_message: Optional[str] = None,
    user_id: Optional[int] = None,
    session_id: Optional[str] = None,
    selected_stores: Optional[List[int]] = None,
    query_model: str = 'gemini-2.5-flash-lite'
) -> Optional[int]:
    """
    Global helper function for logging query activity.
    Can be imported and used directly in API endpoints.
    """
    return activity_logger.log_query(
        query_text=query_text,
        user_id=user_id,
        session_id=session_id,
        selected_stores=selected_stores,
        query_model=query_model,
        results_count=results_count,
        response_time_ms=response_time_ms,
        was_successful=was_successful,
        error_message=error_message
    )

def log_user_interaction(
    interaction_type: str,
    user_id: Optional[int] = None,
    session_id: Optional[str] = None,
    target_type: Optional[str] = None,
    target_id: Optional[int] = None,
    source_query_id: Optional[int] = None,
    interaction_data: Optional[Dict[str, Any]] = None
) -> Optional[int]:
    """
    Global helper function for logging user interactions.
    Can be imported and used directly throughout the application.
    """
    return activity_logger.log_interaction(
        interaction_type=interaction_type,
        user_id=user_id,
        session_id=session_id,
        target_type=target_type,
        target_id=target_id,
        source_query_id=source_query_id,
        interaction_data=interaction_data
    )
