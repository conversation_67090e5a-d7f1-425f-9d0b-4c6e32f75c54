"""
Session Management Utility

Handles session creation, tracking, and management for both authenticated
and anonymous users. Integrates with the user activity logging system.
"""

import uuid
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import Request, Response
from user_activity_logger import UserActivityLogger

class SessionManager:
    """
    Manages user sessions for activity tracking.
    Handles both authenticated and anonymous users.
    """
    
    def __init__(self):
        self.activity_logger = UserActivityLogger()
        self.session_timeout_minutes = 30  # Session timeout
        self.cookie_name = "tilbudsjaegeren_session"
    
    def get_or_create_session_id(self, request: Request, response: Response) -> str:
        """
        Get existing session ID from cookie or create a new one.
        
        Args:
            request: FastAPI request object
            response: FastAPI response object
            
        Returns:
            Session ID string
        """
        # Try to get existing session ID from cookie
        session_id = request.cookies.get(self.cookie_name)
        
        if not session_id:
            # Create new session ID
            session_id = f"sess_{uuid.uuid4().hex}"
            
            # Set session cookie (expires in 30 days)
            response.set_cookie(
                key=self.cookie_name,
                value=session_id,
                max_age=30 * 24 * 60 * 60,  # 30 days
                httponly=True,
                secure=False,  # Set to True in production with HTTPS
                samesite="lax"
            )
        
        return session_id
    
    def start_session(
        self,
        session_id: str,
        request: Request,
        user_id: Optional[int] = None
    ) -> bool:
        """
        Start tracking a new session.
        
        Args:
            session_id: Session identifier
            request: FastAPI request object
            user_id: User ID if authenticated
            
        Returns:
            True if successful
        """
        # Extract browser and device info from request
        user_agent = request.headers.get("user-agent", "")
        browser = self._extract_browser_name(user_agent)
        
        # Get client IP (handle proxy headers)
        ip_address = self._get_client_ip(request)
        
        # Check if this is a returning user (simplified check)
        is_return_session = user_id is not None
        
        return self.activity_logger.start_session(
            session_id=session_id,
            user_id=user_id,
            browser=browser,
            user_agent=user_agent,
            ip_address=ip_address,
            is_return_session=is_return_session
        )
    
    def update_session_activity(
        self,
        session_id: str,
        queries_count: int = 0,
        interactions_count: int = 0,
        pages_viewed: int = 0
    ) -> bool:
        """
        Update session activity metrics.
        This can be called periodically to update session stats.
        
        Args:
            session_id: Session identifier
            queries_count: Total queries in session
            interactions_count: Total interactions in session
            pages_viewed: Total pages viewed in session
            
        Returns:
            True if successful
        """
        # For now, we'll just track these metrics and update on session end
        # In a more sophisticated implementation, we might update the session
        # record periodically or use Redis for real-time tracking
        return True
    
    def end_session(
        self,
        session_id: str,
        queries_count: int = 0,
        interactions_count: int = 0,
        pages_viewed: int = 0
    ) -> bool:
        """
        End a session and calculate final metrics.
        
        Args:
            session_id: Session identifier
            queries_count: Total queries in session
            interactions_count: Total interactions in session
            pages_viewed: Total pages viewed in session
            
        Returns:
            True if successful
        """
        return self.activity_logger.end_session(
            session_id=session_id,
            queries_count=queries_count,
            interactions_count=interactions_count,
            pages_viewed=pages_viewed
        )
    
    def _extract_browser_name(self, user_agent: str) -> str:
        """
        Extract browser name from user agent string.
        
        Args:
            user_agent: User agent string
            
        Returns:
            Browser name
        """
        if not user_agent:
            return "unknown"
        
        user_agent_lower = user_agent.lower()
        
        if "chrome" in user_agent_lower and "edg" not in user_agent_lower:
            return "chrome"
        elif "firefox" in user_agent_lower:
            return "firefox"
        elif "safari" in user_agent_lower and "chrome" not in user_agent_lower:
            return "safari"
        elif "edg" in user_agent_lower:
            return "edge"
        elif "opera" in user_agent_lower:
            return "opera"
        else:
            return "other"
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Get client IP address, handling proxy headers.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Client IP address
        """
        # Check for common proxy headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"


# --- Session Tracking Middleware ---

class SessionTrackingMiddleware:
    """
    Middleware to automatically track sessions for all requests.
    """
    
    def __init__(self):
        self.session_manager = SessionManager()
        self.tracked_sessions = {}  # In-memory session tracking
    
    async def track_request(
        self,
        request: Request,
        response: Response,
        user_id: Optional[int] = None
    ) -> str:
        """
        Track a request and return session ID.
        
        Args:
            request: FastAPI request object
            response: FastAPI response object
            user_id: User ID if authenticated
            
        Returns:
            Session ID
        """
        # Get or create session ID
        session_id = self.session_manager.get_or_create_session_id(request, response)
        
        # Start session if not already tracked
        if session_id not in self.tracked_sessions:
            self.session_manager.start_session(session_id, request, user_id)
            self.tracked_sessions[session_id] = {
                'started_at': datetime.utcnow(),
                'queries_count': 0,
                'interactions_count': 0,
                'pages_viewed': 1,
                'user_id': user_id
            }
        else:
            # Update page view count
            self.tracked_sessions[session_id]['pages_viewed'] += 1
        
        return session_id
    
    def increment_query_count(self, session_id: str):
        """Increment query count for session."""
        if session_id in self.tracked_sessions:
            self.tracked_sessions[session_id]['queries_count'] += 1
    
    def increment_interaction_count(self, session_id: str):
        """Increment interaction count for session."""
        if session_id in self.tracked_sessions:
            self.tracked_sessions[session_id]['interactions_count'] += 1
    
    def cleanup_old_sessions(self, max_age_hours: int = 24):
        """
        Clean up old session tracking data.
        
        Args:
            max_age_hours: Maximum age of sessions to keep in memory
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        sessions_to_remove = []
        
        for session_id, session_data in self.tracked_sessions.items():
            if session_data['started_at'] < cutoff_time:
                # End the session in the database
                self.session_manager.end_session(
                    session_id=session_id,
                    queries_count=session_data['queries_count'],
                    interactions_count=session_data['interactions_count'],
                    pages_viewed=session_data['pages_viewed']
                )
                sessions_to_remove.append(session_id)
        
        # Remove from memory
        for session_id in sessions_to_remove:
            del self.tracked_sessions[session_id]


# --- Global Session Manager Instance ---

# Create global instances for use throughout the application
session_manager = SessionManager()
session_tracking_middleware = SessionTrackingMiddleware()

# --- Helper Functions ---

def get_session_id_from_request(request: Request, response: Response) -> str:
    """
    Helper function to get session ID from request.
    Creates new session if needed.
    """
    return session_manager.get_or_create_session_id(request, response)

def track_page_view(
    request: Request,
    response: Response,
    user_id: Optional[int] = None
) -> str:
    """
    Helper function to track a page view and return session ID.
    """
    return session_tracking_middleware.track_request(request, response, user_id)

def increment_session_query_count(session_id: str):
    """Helper function to increment query count for session."""
    session_tracking_middleware.increment_query_count(session_id)

def increment_session_interaction_count(session_id: str):
    """Helper function to increment interaction count for session."""
    session_tracking_middleware.increment_interaction_count(session_id)
