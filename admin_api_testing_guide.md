# Admin API Testing Guide

## 🧪 **Testing the Admin API Endpoints**

### **Prerequisites**
1. **Admin user account** - You need an admin user to test these endpoints
2. **Authentication token** - JWT token for admin user
3. **Test data** - Some user activity data in the database
4. **API client** - Postman, curl, or similar tool

---

## **🔐 Authentication Setup**

### **Create Admin User (if needed)**
```python
# In your Python console or script
from database import get_db, User
from passlib.context import CryptContext

db = next(get_db())
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

admin_user = User(
    username="<EMAIL>",
    hashed_password=pwd_context.hash("your_admin_password"),
    is_admin=True
)

db.add(admin_user)
db.commit()
print(f"Admin user created with ID: {admin_user.id}")
```

### **Get Authentication Token**
```bash
# Login to get JWT token
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "your_admin_password"
  }'

# Response will include access_token
# Use this token in Authorization header: "Bearer <token>"
```

---

## **📋 Test Cases**

### **1. User Search Endpoint**

#### **Basic User Search**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/search?limit=10"
```

**Expected Response:**
- Status: 200 OK
- JSON with users array, total_count, pagination info
- Each user should have id, username, stats, user_type

#### **Search with Filters**
```bash
# Search for active users
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/search?user_type=active&limit=5"

# Search by username
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/search?search_term=test&limit=5"

# Search with date range
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/search?date_from=2025-01-01T00:00:00Z&limit=5"
```

#### **Pagination Test**
```bash
# First page
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/search?limit=2&offset=0"

# Second page
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/search?limit=2&offset=2"
```

### **2. User Profile Endpoint**

#### **Get User Profile**
```bash
# Replace 1 with actual user ID
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/1/profile"
```

**Expected Response:**
- Status: 200 OK
- Complete user profile with stats, categories, recent activity
- User type classification

#### **Non-existent User**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/99999/profile"
```

**Expected Response:**
- Status: 404 Not Found
- Error message: "User not found"

### **3. User Timeline Endpoint**

#### **Complete Timeline**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/1/timeline?limit=20"
```

#### **Filter by Activity Type**
```bash
# Only queries
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/1/timeline?activity_type=queries&limit=10"

# Only interactions
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/1/timeline?activity_type=interactions&limit=10"

# Only sessions
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/1/timeline?activity_type=sessions&limit=10"
```

#### **Date Range Filter**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/1/timeline?date_from=2025-01-15T00:00:00Z&date_to=2025-01-20T23:59:59Z"
```

### **4. User Queries Endpoint**

#### **Get User Queries**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/1/queries?limit=20"
```

**Expected Response:**
- Status: 200 OK
- Array of queries with full details
- Pagination information

#### **Filter by Date**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/1/queries?date_from=2025-01-15T00:00:00Z&limit=10"
```

### **5. System Analytics Endpoint**

#### **Default Analytics (Last 30 Days)**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/analytics/system"
```

**Expected Response:**
- Status: 200 OK
- System metrics: users, queries, sessions, interactions
- Popular queries and category breakdown

#### **Custom Date Range**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/analytics/system?date_from=2025-01-01T00:00:00Z&date_to=2025-01-20T23:59:59Z"
```

### **6. Query Analytics Endpoint**

#### **Query Analytics**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/analytics/queries?limit=20"
```

**Expected Response:**
- Status: 200 OK
- Popular queries with performance metrics
- Failed queries analysis
- Refinement patterns

### **7. Session Stats Endpoint**

#### **Current Session Statistics**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/session-stats"
```

**Expected Response:**
- Status: 200 OK
- Current session counts and activity metrics

---

## **🔍 Error Testing**

### **Authentication Errors**

#### **No Token**
```bash
curl "http://localhost:8000/api/admin/users/search"
```
**Expected**: 401 Unauthorized

#### **Invalid Token**
```bash
curl -H "Authorization: Bearer invalid_token" \
  "http://localhost:8000/api/admin/users/search"
```
**Expected**: 401 Unauthorized

#### **Non-Admin User**
```bash
# Use token from regular user (not admin)
curl -H "Authorization: Bearer <regular_user_token>" \
  "http://localhost:8000/api/admin/users/search"
```
**Expected**: 403 Forbidden

### **Validation Errors**

#### **Invalid Date Format**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/search?date_from=invalid_date"
```
**Expected**: 400 Bad Request with date format error

#### **Invalid User ID**
```bash
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/abc/profile"
```
**Expected**: 422 Unprocessable Entity

---

## **📊 Data Verification**

### **Check Audit Logging**
After running admin API tests, verify audit logs are created:

```python
from database import get_db, AdminAuditLog

db = next(get_db())
recent_logs = db.query(AdminAuditLog)\
               .order_by(AdminAuditLog.created_at.desc())\
               .limit(10).all()

for log in recent_logs:
    print(f"{log.created_at}: {log.action_type} by admin {log.admin_user_id}")
```

### **Verify Response Data**
1. **User counts** should match database records
2. **Date ranges** should be respected in filters
3. **Pagination** should work correctly
4. **User types** should be classified properly
5. **Analytics metrics** should be accurate

---

## **🚀 Performance Testing**

### **Load Testing**
```bash
# Test with larger datasets
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/users/search?limit=1000"

# Test complex analytics queries
curl -H "Authorization: Bearer <your_admin_token>" \
  "http://localhost:8000/api/admin/analytics/system?date_from=2024-01-01T00:00:00Z"
```

### **Response Time Testing**
Monitor response times for:
- User search with filters: < 500ms
- User profile: < 200ms
- System analytics: < 1000ms
- User timeline: < 300ms

---

## **✅ Test Checklist**

### **Functionality Tests**
- [ ] User search works with all filters
- [ ] User profile returns complete data
- [ ] User timeline shows chronological activities
- [ ] User queries include all details
- [ ] System analytics show accurate metrics
- [ ] Query analytics include performance data
- [ ] Session stats reflect current state

### **Security Tests**
- [ ] Authentication required for all endpoints
- [ ] Admin role required (non-admin users rejected)
- [ ] Invalid tokens rejected
- [ ] Audit logging works for all actions

### **Error Handling Tests**
- [ ] Invalid user IDs return 404
- [ ] Invalid date formats return 400
- [ ] Database errors handled gracefully
- [ ] Proper error messages returned

### **Performance Tests**
- [ ] Pagination works correctly
- [ ] Large datasets handled efficiently
- [ ] Response times within acceptable limits
- [ ] Database queries optimized

---

## **🎯 Success Criteria**

✅ **All endpoints return expected data formats**
✅ **Authentication and authorization work correctly**
✅ **Error handling is robust and informative**
✅ **Performance is acceptable for admin use**
✅ **Audit logging captures all admin actions**
✅ **Data accuracy verified against database**

**Admin API endpoints are ready for production use!** 🚀
