"use client";

import { useState } from 'react';
import BottomTabNavigation, { TabType } from './BottomTabNavigation';
import HamburgerMenu from './HamburgerMenu';

// Tab content components (we'll create these)
import SearchTab from '@/components/tabs/SearchTab';
import ShoppingTab from '@/components/tabs/ShoppingTab';
import FavoritesTab from '@/components/tabs/FavoritesTab';
import ProfileTab from '@/components/tabs/ProfileTab';

interface MobileLayoutProps {
  children?: React.ReactNode;
}

export default function MobileLayout({ children }: MobileLayoutProps) {
  const [activeTab, setActiveTab] = useState<TabType>('search');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [shoppingListCount, setShoppingListCount] = useState(0);
  const [favoritesCount, setFavoritesCount] = useState(0);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    // Close menu if open
    if (isMenuOpen) {
      setIsMenuOpen(false);
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'search':
        return <SearchTab />;
      case 'shopping':
        return <ShoppingTab onCountChange={setShoppingListCount} />;
      case 'favorites':
        return <FavoritesTab onCountChange={setFavoritesCount} />;
      case 'profile':
        return <ProfileTab />;
      default:
        return <SearchTab />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Hamburger Menu */}
      <HamburgerMenu isOpen={isMenuOpen} onToggle={toggleMenu} />
      
      {/* Main Content Area */}
      <main className="flex-1 pb-20 overflow-y-auto">
        {/* Tab Content */}
        <div className="min-h-full">
          {renderTabContent()}
        </div>
      </main>
      
      {/* Bottom Tab Navigation */}
      <BottomTabNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        shoppingListCount={shoppingListCount}
        favoritesCount={favoritesCount}
      />
    </div>
  );
}
