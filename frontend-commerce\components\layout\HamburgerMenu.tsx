"use client";

import { useState, useEffect } from 'react';
import { 
  Bars3Icon, 
  XMarkIcon,
  InformationCircleIcon,
  UserCircleIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';

interface HamburgerMenuProps {
  isOpen: boolean;
  onToggle: () => void;
}

export default function HamburgerMenu({ isOpen, onToggle }: HamburgerMenuProps) {
  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onToggle();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onToggle]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const menuItems = [
    {
      icon: InformationCircleIcon,
      label: 'Om Tilbudsjægeren',
      href: '/about',
      description: 'Læs om vores mission'
    },
    {
      icon: UserCircleIcon,
      label: 'Min Profil',
      href: '/profile',
      description: 'Indstillinger og præferencer'
    },
    {
      icon: ChartBarIcon,
      label: 'Mine Data',
      href: '/data',
      description: 'Se dine søgninger og aktivitet'
    },
    {
      icon: ShieldCheckIcon,
      label: 'Privatliv',
      href: '/privacy',
      description: 'Privatlivspolitik og sikkerhed'
    },
    {
      icon: Cog6ToothIcon,
      label: 'Indstillinger',
      href: '/settings',
      description: 'App indstillinger'
    },
    {
      icon: QuestionMarkCircleIcon,
      label: 'Hjælp & Support',
      href: '/help',
      description: 'Få hjælp til at bruge appen'
    }
  ];

  return (
    <>
      {/* Hamburger Button */}
      <button
        onClick={onToggle}
        className={`
          fixed top-4 left-4 z-50 p-2 rounded-lg transition-all duration-200
          ${isOpen 
            ? 'bg-white text-gray-900 shadow-lg' 
            : 'bg-white/80 backdrop-blur-sm text-gray-700 hover:bg-white hover:shadow-md'
          }
        `}
        aria-label={isOpen ? 'Luk menu' : 'Åbn menu'}
      >
        {isOpen ? (
          <XMarkIcon className="w-6 h-6" />
        ) : (
          <Bars3Icon className="w-6 h-6" />
        )}
      </button>

      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300"
          onClick={onToggle}
        />
      )}

      {/* Menu Panel */}
      <div className={`
        fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50
        transform transition-transform duration-300 ease-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">T</span>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Tilbudsjægeren</h2>
              <p className="text-sm text-gray-600">Find de bedste tilbud</p>
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="py-4">
          {menuItems.map((item) => (
            <button
              key={item.href}
              onClick={() => {
                // TODO: Implement navigation
                console.log(`Navigate to ${item.href}`);
                onToggle();
              }}
              className="w-full px-6 py-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors duration-150 text-left"
            >
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                <item.icon className="w-5 h-5 text-gray-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900">{item.label}</div>
                <div className="text-xs text-gray-500 truncate">{item.description}</div>
              </div>
            </button>
          ))}
        </div>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-center">
            <p className="text-xs text-gray-500">Version 1.0.0</p>
            <p className="text-xs text-gray-400 mt-1">© 2025 Tilbudsjægeren</p>
          </div>
        </div>
      </div>
    </>
  );
}
