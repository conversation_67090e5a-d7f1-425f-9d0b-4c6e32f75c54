"""
Constants and Enums for User Activity Tracking

This file contains reference constants for interaction types, query categories,
and other standardized values used in the user activity tracking system.
"""

# Interaction Types for UserInteraction.interaction_type
INTERACTION_TYPES = {
    # Query-Related
    'query_submitted': 'User submitted a search query',
    'query_refined': 'User modified their search',
    'query_failed': 'Query returned no results or error',
    
    # Catalog & Product Interactions
    'catalog_viewed': 'User opened a catalog PDF',
    'catalog_downloaded': 'User downloaded a catalog',
    'product_clicked': 'User clicked on a product result',
    'product_details_viewed': 'User viewed product details',
    'store_logo_clicked': 'User clicked on store logo/selector',
    
    # Navigation & Engagement
    'page_viewed': 'User visited a page',
    'store_selected': 'User selected/deselected stores',
    'favorite_added': 'User saved a favorite query',
    'favorite_used': 'User executed a favorite query',
    
    # Admin Actions (for audit trail)
    'admin_user_viewed': 'Admin viewed user profile',
    'admin_data_exported': 'Admin exported user data',
    'admin_preference_changed': 'Admin modified user settings'
}

# Query Categories for UserQuery.query_category
QUERY_CATEGORIES = {
    'food_beverages': 'Food and drink items',
    'household_cleaning': 'Cleaning supplies, detergents',
    'personal_care': 'Beauty, hygiene products',
    'electronics': 'Tech products, appliances',
    'home_garden': 'Home improvement, gardening',
    'clothing_accessories': 'Fashion items',
    'health_pharmacy': 'Medicine, health products',
    'baby_kids': 'Children\'s products',
    'pets': 'Pet food and supplies',
    'seasonal': 'Holiday, seasonal items'
}

# Query Intent Types for UserQuery.query_intent
QUERY_INTENTS = {
    'price_comparison': 'Looking for best price',
    'specific_product': 'Searching for exact item',
    'category_browse': 'Exploring product category',
    'recipe_ingredients': 'Meal planning',
    'brand_search': 'Looking for specific brand',
    'deal_hunting': 'Looking for discounts/offers'
}

# Preference Types for UserPreferencesHistory.preference_type
PREFERENCE_TYPES = {
    'preferred_stores': 'User\'s preferred store selection',
    'locale': 'User\'s language and country preference',
    'currency': 'User\'s preferred currency',
    'query_model': 'User\'s preferred AI model',
    'ui_theme': 'User\'s interface theme preference',
    'notification_settings': 'User\'s notification preferences'
}

# Admin Audit Action Types for AdminAuditLog.action_type
ADMIN_AUDIT_ACTIONS = {
    'user_viewed': 'Admin viewed user profile',
    'user_searched': 'Admin searched for users',
    'query_history_viewed': 'Admin viewed user query history',
    'session_data_viewed': 'Admin viewed user session data',
    'interaction_data_viewed': 'Admin viewed user interaction data',
    'data_exported': 'Admin exported user data',
    'user_preference_changed': 'Admin modified user preferences',
    'user_created': 'Admin created new user account',
    'user_deleted': 'Admin deleted user account',
    'bulk_operation': 'Admin performed bulk operation'
}

# Device Types for UserSession.device_type
DEVICE_TYPES = {
    'mobile': 'Mobile phone',
    'tablet': 'Tablet device',
    'desktop': 'Desktop computer',
    'unknown': 'Unknown device type'
}

# Languages for UserQuery.detected_language and user_locale
SUPPORTED_LANGUAGES = {
    'da': 'Danish',
    'en': 'English',
    'sv': 'Swedish',
    'no': 'Norwegian',
    'de': 'German'
}

# Locales for UserQuery.user_locale
SUPPORTED_LOCALES = {
    'da-DK': 'Danish (Denmark)',
    'en-US': 'English (United States)',
    'en-GB': 'English (United Kingdom)',
    'sv-SE': 'Swedish (Sweden)',
    'no-NO': 'Norwegian (Norway)',
    'de-DE': 'German (Germany)'
}

# Change Reasons for UserPreferencesHistory.change_reason
CHANGE_REASONS = {
    'user_selection': 'User manually changed setting',
    'auto_detection': 'System automatically detected preference',
    'admin_update': 'Administrator updated setting',
    'migration': 'Data migration or system update',
    'default_assignment': 'Default value assigned to new user'
}

# Helper Functions for Categorization

def categorize_query(query_text: str) -> str:
    """
    Automatically categorize a query based on keywords.
    Returns the most likely category or 'general' if no match.
    """
    query_lower = query_text.lower()
    
    # Food & Beverages keywords
    food_keywords = ['mad', 'øl', 'vin', 'brød', 'mælk', 'kød', 'fisk', 'frugt', 'grøntsager', 
                     'kaffe', 'te', 'juice', 'pizza', 'pasta', 'ris', 'kylling', 'ost']
    
    # Household & Cleaning keywords  
    household_keywords = ['rengøring', 'vaskemiddel', 'opvaskemiddel', 'toiletpapir', 
                         'køkkenrulle', 'sæbe', 'shampoo', 'tandpasta']
    
    # Electronics keywords
    electronics_keywords = ['tv', 'computer', 'telefon', 'tablet', 'høretelefoner', 
                           'kamera', 'playstation', 'xbox', 'nintendo']
    
    # Check for matches
    if any(keyword in query_lower for keyword in food_keywords):
        return 'food_beverages'
    elif any(keyword in query_lower for keyword in household_keywords):
        return 'household_cleaning'
    elif any(keyword in query_lower for keyword in electronics_keywords):
        return 'electronics'
    
    return 'general'

def detect_query_intent(query_text: str, selected_stores_count: int) -> str:
    """
    Detect the likely intent behind a query.
    """
    query_lower = query_text.lower()
    
    # Price comparison indicators
    if any(word in query_lower for word in ['billigst', 'pris', 'tilbud', 'rabat', 'sammenlign']):
        return 'price_comparison'
    
    # Brand search indicators
    if any(word in query_lower for word in ['coca cola', 'nike', 'samsung', 'apple', 'sony']):
        return 'brand_search'
    
    # Recipe/meal planning indicators
    if any(word in query_lower for word in ['ingredienser', 'opskrift', 'middag', 'frokost']):
        return 'recipe_ingredients'
    
    # Multiple stores selected suggests comparison shopping
    if selected_stores_count > 2:
        return 'price_comparison'
    
    # Default to specific product search
    return 'specific_product'

def get_device_type_from_user_agent(user_agent: str) -> str:
    """
    Detect device type from user agent string.
    """
    if not user_agent:
        return 'unknown'
    
    user_agent_lower = user_agent.lower()
    
    if any(mobile in user_agent_lower for mobile in ['mobile', 'android', 'iphone']):
        return 'mobile'
    elif any(tablet in user_agent_lower for tablet in ['tablet', 'ipad']):
        return 'tablet'
    else:
        return 'desktop'

# Validation Functions

def is_valid_interaction_type(interaction_type: str) -> bool:
    """Check if interaction type is valid."""
    return interaction_type in INTERACTION_TYPES

def is_valid_query_category(category: str) -> bool:
    """Check if query category is valid."""
    return category in QUERY_CATEGORIES

def is_valid_preference_type(preference_type: str) -> bool:
    """Check if preference type is valid."""
    return preference_type in PREFERENCE_TYPES

def is_valid_locale(locale: str) -> bool:
    """Check if locale is supported."""
    return locale in SUPPORTED_LOCALES

# Export lists for easy iteration
INTERACTION_TYPE_LIST = list(INTERACTION_TYPES.keys())
QUERY_CATEGORY_LIST = list(QUERY_CATEGORIES.keys())
QUERY_INTENT_LIST = list(QUERY_INTENTS.keys())
PREFERENCE_TYPE_LIST = list(PREFERENCE_TYPES.keys())
ADMIN_AUDIT_ACTION_LIST = list(ADMIN_AUDIT_ACTIONS.keys())
DEVICE_TYPE_LIST = list(DEVICE_TYPES.keys())
SUPPORTED_LANGUAGE_LIST = list(SUPPORTED_LANGUAGES.keys())
SUPPORTED_LOCALE_LIST = list(SUPPORTED_LOCALES.keys())
CHANGE_REASON_LIST = list(CHANGE_REASONS.keys())
