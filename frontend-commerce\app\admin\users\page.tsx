"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import Link from 'next/link';

// Define the User interface based on our user activity API
interface User {
  id: number;
  username: string;
  is_admin: boolean;
  created_at: string | null;
  total_queries: number;
  total_sessions: number;
  last_activity: string | null;
  user_type: string;
}

interface UsersResponse {
  users: User[];
  total_count: number;
  limit: number;
  offset: number;
}

// Updated function to use our new user activity API
async function getUsers(token: string): Promise<User[]> {
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';
  const response = await fetch(`${API_BASE}/api/admin/users/search?limit=50`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch users: ${response.status} ${response.statusText}`);
  }

  const data: UsersResponse = await response.json();
  return data.users;
}

export default function AdminUsersPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect non-admin users
  useEffect(() => {
    if (!isLoading && (!user || !user.is_admin)) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  // Load users
  useEffect(() => {
    if (user?.is_admin) {
      const token = localStorage.getItem('token');
      if (token) {
        const loadUsers = async () => {
          try {
            setUsersLoading(true);
            const data = await getUsers(token);
            setUsers(data);
          } catch (err) {
            console.error('Failed to load users:', err);
            setError('Kunne ikke indlæse brugere');
          } finally {
            setUsersLoading(false);
          }
        };

        loadUsers();
      }
    }
  }, [user]);

  if (isLoading || !user || !user.is_admin) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Bruger Styring</h1>
        <Link
          href="/admin/user-activity"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          📊 Se Aktivitetsdashboard
        </Link>
      </div>

      {/* User Statistics Summary */}
      {!usersLoading && users.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-2xl font-bold text-gray-900">{users.length}</div>
            <div className="text-sm text-gray-500">Total Brugere</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-2xl font-bold text-green-600">
              {users.filter(u => u.user_type === 'power_user' || u.user_type === 'regular_user').length}
            </div>
            <div className="text-sm text-gray-500">Aktive Brugere</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-2xl font-bold text-blue-600">
              {users.reduce((sum, u) => sum + u.total_queries, 0)}
            </div>
            <div className="text-sm text-gray-500">Total Søgninger</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-2xl font-bold text-purple-600">
              {users.filter(u => u.is_admin).length}
            </div>
            <div className="text-sm text-gray-500">Admin Brugere</div>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {usersLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rolle</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Brugertype</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Søgninger</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessioner</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sidste Aktivitet</th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Handlinger</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((u) => (
                <tr key={u.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{u.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{u.username}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${u.is_admin ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {u.is_admin ? 'Admin' : 'Bruger'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      u.user_type === 'power_user' ? 'bg-purple-100 text-purple-800' :
                      u.user_type === 'regular_user' ? 'bg-blue-100 text-blue-800' :
                      u.user_type === 'casual_user' ? 'bg-green-100 text-green-800' :
                      u.user_type === 'new_user' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {u.user_type === 'power_user' ? 'Power User' :
                       u.user_type === 'regular_user' ? 'Almindelig' :
                       u.user_type === 'casual_user' ? 'Lejlighedsvis' :
                       u.user_type === 'new_user' ? 'Ny' :
                       u.user_type.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{u.total_queries}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{u.total_sessions}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {u.last_activity ? new Date(u.last_activity).toLocaleDateString('da-DK') : 'Aldrig'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      href={`/admin/user-activity?user=${u.id}`}
                      className="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      Se Aktivitet
                    </Link>
                    <button className="text-indigo-600 hover:text-indigo-900">Rediger</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-6">
        <Link href="/admin" className="text-indigo-600 hover:text-indigo-900 flex items-center gap-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          Tilbage til Admin Dashboard
        </Link>
      </div>
    </div>
  );
}
