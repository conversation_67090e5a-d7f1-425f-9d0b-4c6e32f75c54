"use client";

import { useState } from 'react';
import { 
  UserCircleIcon, 
  Cog6ToothIcon, 
  ChartBarIcon,
  BellIcon,
  ShieldCheckIcon,
  QuestionMarkCircleIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';

export default function ProfileTab() {
  const [user] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    memberSince: '2025-01-15',
    totalSearches: 47,
    favoriteStore: 'Netto'
  });

  const profileSections = [
    {
      title: 'Konto',
      items: [
        {
          icon: UserCircleIcon,
          label: 'Rediger profil',
          description: 'Navn, email og præferencer',
          action: () => console.log('Edit profile')
        },
        {
          icon: Cog6ToothIcon,
          label: 'Indstillinger',
          description: 'App indstillinger og præferencer',
          action: () => console.log('Settings')
        },
        {
          icon: BellIcon,
          label: 'Notifikationer',
          description: 'Tilbudsvarsler og opdateringer',
          action: () => console.log('Notifications')
        }
      ]
    },
    {
      title: 'Data & Privatliv',
      items: [
        {
          icon: ChartBarIcon,
          label: 'Mine data',
          description: 'Se dine søgninger og aktivitet',
          action: () => console.log('My data')
        },
        {
          icon: ShieldCheckIcon,
          label: 'Privatliv',
          description: 'Privatlivspolitik og datasikkerhed',
          action: () => console.log('Privacy')
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          icon: QuestionMarkCircleIcon,
          label: 'Hjælp & Support',
          description: 'FAQ og kontakt information',
          action: () => console.log('Help')
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white px-4 pt-16 pb-6 shadow-sm">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <UserCircleIcon className="w-10 h-10 text-blue-600" />
            </div>
            <div className="flex-1">
              <h1 className="text-xl font-semibold text-gray-900">{user.name}</h1>
              <p className="text-gray-600">{user.email}</p>
              <p className="text-sm text-gray-500">
                Medlem siden {new Date(user.memberSince).toLocaleDateString('da-DK')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="px-4 py-6">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Din aktivitet</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{user.totalSearches}</div>
                <div className="text-sm text-gray-600">Søgninger</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">📍</div>
                <div className="text-sm text-gray-600">Favorit: {user.favoriteStore}</div>
              </div>
            </div>
          </div>

          {/* Profile Sections */}
          <div className="space-y-6">
            {profileSections.map((section) => (
              <div key={section.title} className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
                  <h3 className="font-medium text-gray-900">{section.title}</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {section.items.map((item) => (
                    <button
                      key={item.label}
                      onClick={item.action}
                      className="w-full px-4 py-4 flex items-center space-x-4 hover:bg-gray-50 transition-colors text-left"
                    >
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        <item.icon className="w-5 h-5 text-gray-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900">{item.label}</div>
                        <div className="text-xs text-gray-500">{item.description}</div>
                      </div>
                      <div className="text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Logout */}
          <div className="mt-8">
            <button
              onClick={() => {
                // TODO: Implement logout
                console.log('Logout');
              }}
              className="w-full bg-white rounded-lg shadow-sm p-4 flex items-center justify-center space-x-3 text-red-600 hover:bg-red-50 transition-colors"
            >
              <ArrowRightOnRectangleIcon className="w-5 h-5" />
              <span className="font-medium">Log ud</span>
            </button>
          </div>

          {/* App Info */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">Tilbudsjægeren v1.0.0</p>
            <p className="text-xs text-gray-400 mt-1">© 2025 Tilbudsjægeren</p>
          </div>
        </div>
      </div>
    </div>
  );
}
