"""
Advanced Interaction Logging

Enhanced interaction logging for detailed user engagement tracking.
Captures product clicks, catalog downloads, search refinements, and other
user behaviors that provide deep insights into user interaction patterns.
"""

import json
import time
import logging
from datetime import datetime
from typing import Optional, Dict, List, Any, Union
from fastapi import Request
from user_activity_logger import log_user_interaction
from session_tracking_middleware import get_session_id_from_request

logger = logging.getLogger(__name__)

class AdvancedInteractionLogger:
    """
    Enhanced interaction logging for detailed user engagement tracking.
    Provides specialized methods for different types of user interactions.
    """
    
    def __init__(self):
        self.interaction_start_times = {}  # Track interaction durations
    
    # --- Product Interaction Logging ---
    
    def log_product_click(
        self,
        product_id: int,
        product_name: str,
        product_price: Optional[float] = None,
        store_id: Optional[int] = None,
        store_name: Optional[str] = None,
        source_query_id: Optional[int] = None,
        position_in_results: Optional[int] = None,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """
        Log when user clicks on a product in search results.
        
        Args:
            product_id: Unique product identifier
            product_name: Name of the product
            product_price: Product price if available
            store_id: Store where product is found
            store_name: Name of the store
            source_query_id: Query that led to this product
            position_in_results: Position in search results (1-based)
            request: FastAPI request object
            user_id: User ID if authenticated
            session_id: Session identifier
            
        Returns:
            Interaction ID if successful
        """
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "product_name": product_name,
            "product_price": product_price,
            "store_id": store_id,
            "store_name": store_name,
            "position_in_results": position_in_results,
            "click_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="product_clicked",
            user_id=user_id,
            session_id=session_id,
            target_type="product",
            target_id=product_id,
            source_query_id=source_query_id,
            interaction_data=interaction_data
        )
    
    def log_product_details_view(
        self,
        product_id: int,
        product_name: str,
        view_duration_seconds: Optional[int] = None,
        source_query_id: Optional[int] = None,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log when user views detailed product information."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "product_name": product_name,
            "view_duration_seconds": view_duration_seconds,
            "view_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="product_details_viewed",
            user_id=user_id,
            session_id=session_id,
            target_type="product",
            target_id=product_id,
            source_query_id=source_query_id,
            interaction_data=interaction_data
        )
    
    # --- Catalog Interaction Logging ---
    
    def log_catalog_download(
        self,
        catalog_id: int,
        catalog_title: str,
        store_id: int,
        store_name: str,
        file_size_bytes: Optional[int] = None,
        source_query_id: Optional[int] = None,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log when user downloads a catalog PDF."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "catalog_title": catalog_title,
            "store_id": store_id,
            "store_name": store_name,
            "file_size_bytes": file_size_bytes,
            "download_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="catalog_downloaded",
            user_id=user_id,
            session_id=session_id,
            target_type="catalog",
            target_id=catalog_id,
            source_query_id=source_query_id,
            interaction_data=interaction_data
        )
    
    def start_catalog_view_timer(
        self,
        catalog_id: int,
        session_id: str
    ):
        """Start timing how long user views a catalog."""
        timer_key = f"{session_id}_{catalog_id}"
        self.interaction_start_times[timer_key] = time.time()
    
    def log_catalog_view_with_duration(
        self,
        catalog_id: int,
        catalog_title: str,
        store_id: int,
        store_name: str,
        source_query_id: Optional[int] = None,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log catalog view with calculated duration."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        # Calculate view duration
        timer_key = f"{session_id}_{catalog_id}"
        view_duration_seconds = None
        if timer_key in self.interaction_start_times:
            view_duration_seconds = int(time.time() - self.interaction_start_times[timer_key])
            del self.interaction_start_times[timer_key]
        
        interaction_data = {
            "catalog_title": catalog_title,
            "store_id": store_id,
            "store_name": store_name,
            "view_duration_seconds": view_duration_seconds,
            "view_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="catalog_viewed",
            user_id=user_id,
            session_id=session_id,
            target_type="catalog",
            target_id=catalog_id,
            source_query_id=source_query_id,
            interaction_data=interaction_data
        )
    
    # --- Search Behavior Logging ---
    
    def log_query_refinement(
        self,
        original_query: str,
        refined_query: str,
        original_query_id: Optional[int] = None,
        refinement_type: str = "manual",  # "manual", "suggestion_click", "auto_complete"
        time_between_queries_seconds: Optional[int] = None,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log when user refines their search query."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "original_query": original_query,
            "refined_query": refined_query,
            "refinement_type": refinement_type,
            "time_between_queries_seconds": time_between_queries_seconds,
            "refinement_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="query_refined",
            user_id=user_id,
            session_id=session_id,
            source_query_id=original_query_id,
            interaction_data=interaction_data
        )
    
    def log_search_suggestion_click(
        self,
        suggestion_text: str,
        suggestion_position: int,
        original_query: str,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log when user clicks on a search suggestion."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "suggestion_text": suggestion_text,
            "suggestion_position": suggestion_position,
            "original_query": original_query,
            "click_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="search_suggestion_clicked",
            user_id=user_id,
            session_id=session_id,
            interaction_data=interaction_data
        )
    
    # --- Store and Filter Interaction Logging ---
    
    def log_store_filter_change(
        self,
        previous_stores: List[int],
        new_stores: List[int],
        change_type: str = "manual",  # "manual", "select_all", "clear_all"
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log when user changes store selection filters."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        # Calculate changes
        added_stores = list(set(new_stores) - set(previous_stores))
        removed_stores = list(set(previous_stores) - set(new_stores))
        
        interaction_data = {
            "previous_stores": previous_stores,
            "new_stores": new_stores,
            "added_stores": added_stores,
            "removed_stores": removed_stores,
            "change_type": change_type,
            "store_count": len(new_stores),
            "change_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="store_filter_changed",
            user_id=user_id,
            session_id=session_id,
            interaction_data=interaction_data
        )
    
    # --- Navigation and UI Interaction Logging ---
    
    def log_page_navigation(
        self,
        from_page: str,
        to_page: str,
        navigation_method: str = "click",  # "click", "back_button", "direct_url"
        time_on_previous_page_seconds: Optional[int] = None,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log user navigation between pages."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "from_page": from_page,
            "to_page": to_page,
            "navigation_method": navigation_method,
            "time_on_previous_page_seconds": time_on_previous_page_seconds,
            "navigation_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="page_navigation",
            user_id=user_id,
            session_id=session_id,
            page_url=to_page,
            referrer_url=from_page,
            interaction_data=interaction_data
        )
    
    def log_ui_element_interaction(
        self,
        element_type: str,  # "button", "link", "dropdown", "modal", etc.
        element_id: str,
        action: str,  # "click", "hover", "focus", "scroll", etc.
        element_text: Optional[str] = None,
        page_url: Optional[str] = None,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log interactions with specific UI elements."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "element_type": element_type,
            "element_id": element_id,
            "action": action,
            "element_text": element_text,
            "interaction_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="ui_element_interaction",
            user_id=user_id,
            session_id=session_id,
            page_url=page_url,
            interaction_data=interaction_data
        )
    
    # --- Error and Performance Logging ---
    
    def log_user_error_encounter(
        self,
        error_type: str,  # "404", "500", "timeout", "validation", etc.
        error_message: str,
        page_url: Optional[str] = None,
        user_action_before_error: Optional[str] = None,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log when user encounters an error."""
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "error_type": error_type,
            "error_message": error_message,
            "user_action_before_error": user_action_before_error,
            "error_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="error_encountered",
            user_id=user_id,
            session_id=session_id,
            page_url=page_url,
            interaction_data=interaction_data
        )
    
    def log_slow_response_experience(
        self,
        response_time_ms: int,
        action_type: str,  # "query", "page_load", "catalog_view", etc.
        threshold_ms: int = 3000,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> Optional[int]:
        """Log when user experiences slow response times."""
        if response_time_ms < threshold_ms:
            return None  # Only log slow responses
        
        if request and not session_id:
            session_id = get_session_id_from_request(request)
        
        interaction_data = {
            "response_time_ms": response_time_ms,
            "action_type": action_type,
            "threshold_ms": threshold_ms,
            "slow_response_timestamp": datetime.utcnow().isoformat()
        }
        
        return log_user_interaction(
            interaction_type="slow_response_experienced",
            user_id=user_id,
            session_id=session_id,
            interaction_data=interaction_data
        )


# --- Global Advanced Logger Instance ---
advanced_interaction_logger = AdvancedInteractionLogger()

# --- Convenience Functions for Easy Integration ---

def log_product_click(product_id: int, product_name: str, **kwargs) -> Optional[int]:
    """Convenience function for logging product clicks."""
    return advanced_interaction_logger.log_product_click(product_id, product_name, **kwargs)

def log_catalog_download(catalog_id: int, catalog_title: str, store_id: int, store_name: str, **kwargs) -> Optional[int]:
    """Convenience function for logging catalog downloads."""
    return advanced_interaction_logger.log_catalog_download(catalog_id, catalog_title, store_id, store_name, **kwargs)

def log_query_refinement(original_query: str, refined_query: str, **kwargs) -> Optional[int]:
    """Convenience function for logging query refinements."""
    return advanced_interaction_logger.log_query_refinement(original_query, refined_query, **kwargs)

def log_store_filter_change(previous_stores: List[int], new_stores: List[int], **kwargs) -> Optional[int]:
    """Convenience function for logging store filter changes."""
    return advanced_interaction_logger.log_store_filter_change(previous_stores, new_stores, **kwargs)
