"""
Session Tracking Middleware for FastAPI

Automatically tracks user sessions across all requests, manages session lifecycle,
and provides seamless integration with the user activity logging system.
"""

import uuid
import time
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Callable
from fastapi import Request, Response
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGI<PERSON>pp

from user_activity_logger import Use<PERSON><PERSON>ct<PERSON><PERSON>ogger
from session_manager import Session<PERSON>anager
from user_activity_constants import get_device_type_from_user_agent

logger = logging.getLogger(__name__)

class SessionTrackingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to automatically track user sessions for all requests.
    Handles session creation, management, and cleanup.
    """
    
    def __init__(
        self,
        app: ASGIApp,
        session_timeout_minutes: int = 30,
        cleanup_interval_hours: int = 24,
        exclude_paths: Optional[list] = None
    ):
        """
        Initialize session tracking middleware.
        
        Args:
            app: FastAPI application instance
            session_timeout_minutes: Session timeout in minutes
            cleanup_interval_hours: How often to cleanup old sessions
            exclude_paths: List of paths to exclude from session tracking
        """
        super().__init__(app)
        self.session_manager = SessionManager()
        self.activity_logger = UserActivityLogger()
        self.session_timeout_minutes = session_timeout_minutes
        self.cleanup_interval_hours = cleanup_interval_hours
        
        # Default excluded paths (static files, health checks, etc.)
        self.exclude_paths = exclude_paths or [
            "/favicon.ico",
            "/robots.txt",
            "/health",
            "/metrics",
            "/static/",
            "/docs",
            "/redoc",
            "/openapi.json"
        ]
        
        # In-memory session tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.last_cleanup = datetime.utcnow()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process each request and handle session tracking.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/endpoint in chain
            
        Returns:
            Response object with session tracking applied
        """
        # Skip session tracking for excluded paths
        if self._should_exclude_path(request.url.path):
            return await call_next(request)
        
        # Skip session tracking for OPTIONS requests (CORS preflight)
        if request.method == "OPTIONS":
            return await call_next(request)
        
        start_time = time.time()
        session_id = None
        
        try:
            # Get or create session
            response = Response()  # Temporary response for session management
            session_id = self._get_or_create_session(request, response)
            
            # Track the request
            await self._track_request(request, session_id)
            
            # Process the actual request
            response = await call_next(request)
            
            # Apply session cookie to the actual response
            self._apply_session_cookie(response, session_id)
            
            # Update session activity
            await self._update_session_activity(session_id, request, response, start_time)
            
            # Periodic cleanup
            await self._periodic_cleanup()
            
            return response
            
        except Exception as e:
            logger.error(f"Error in session tracking middleware: {str(e)}")
            # Don't let session tracking break the application
            try:
                response = await call_next(request)
                return response
            except Exception as original_error:
                # If the original request also failed, return the original error
                raise original_error
    
    def _should_exclude_path(self, path: str) -> bool:
        """Check if path should be excluded from session tracking."""
        return any(excluded in path for excluded in self.exclude_paths)
    
    def _get_or_create_session(self, request: Request, response: Response) -> str:
        """Get existing session ID or create a new one."""
        # Try to get existing session ID from cookie
        session_id = request.cookies.get("tilbudsjaegeren_session")
        
        if not session_id or session_id not in self.active_sessions:
            # Create new session ID
            session_id = f"sess_{uuid.uuid4().hex}"
            
            # Initialize session data
            self.active_sessions[session_id] = {
                'created_at': datetime.utcnow(),
                'last_activity': datetime.utcnow(),
                'user_id': None,
                'queries_count': 0,
                'interactions_count': 0,
                'pages_viewed': 0,
                'device_type': None,
                'browser': None,
                'ip_address': None,
                'user_agent': request.headers.get("user-agent", ""),
                'started_in_db': False
            }
        
        return session_id
    
    def _apply_session_cookie(self, response: Response, session_id: str):
        """Apply session cookie to response."""
        response.set_cookie(
            key="tilbudsjaegeren_session",
            value=session_id,
            max_age=30 * 24 * 60 * 60,  # 30 days
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite="lax"
        )
    
    async def _track_request(self, request: Request, session_id: str):
        """Track the incoming request."""
        session_data = self.active_sessions.get(session_id, {})
        
        # Extract user info if available (from JWT token, etc.)
        user_id = await self._extract_user_id(request)
        
        # Update session data
        session_data.update({
            'last_activity': datetime.utcnow(),
            'user_id': user_id,
            'pages_viewed': session_data.get('pages_viewed', 0) + 1,
            'device_type': get_device_type_from_user_agent(
                request.headers.get("user-agent", "")
            ),
            'browser': self._extract_browser_name(
                request.headers.get("user-agent", "")
            ),
            'ip_address': self._get_client_ip(request)
        })
        
        # Start session in database if not already started
        if not session_data.get('started_in_db', False):
            success = self.session_manager.start_session(
                session_id=session_id,
                request=request,
                user_id=user_id
            )
            if success:
                session_data['started_in_db'] = True
        
        self.active_sessions[session_id] = session_data
    
    async def _update_session_activity(
        self,
        session_id: str,
        request: Request,
        response: Response,
        start_time: float
    ):
        """Update session activity after request processing."""
        if session_id not in self.active_sessions:
            return
        
        session_data = self.active_sessions[session_id]
        
        # Check if this was a query request
        if request.url.path in ["/ask", "/query"]:
            session_data['queries_count'] = session_data.get('queries_count', 0) + 1
        
        # Check if this was an interaction request
        if any(path in request.url.path for path in ["/catalogs/", "/log-store-selection"]):
            session_data['interactions_count'] = session_data.get('interactions_count', 0) + 1
        
        # Update last activity
        session_data['last_activity'] = datetime.utcnow()
        
        self.active_sessions[session_id] = session_data
    
    async def _extract_user_id(self, request: Request) -> Optional[int]:
        """
        Extract user ID from request (JWT token, session, etc.).
        This is a simplified version - you might need to integrate with your auth system.
        """
        try:
            # Try to get user from Authorization header
            auth_header = request.headers.get("authorization")
            if auth_header and auth_header.startswith("Bearer "):
                # You would decode JWT token here
                # For now, return None (anonymous user)
                pass
            
            # Try to get user from cookies or other auth mechanism
            # This depends on your authentication implementation
            
            return None  # Return None for anonymous users
            
        except Exception as e:
            logger.warning(f"Error extracting user ID: {str(e)}")
            return None
    
    def _extract_browser_name(self, user_agent: str) -> str:
        """Extract browser name from user agent string."""
        if not user_agent:
            return "unknown"
        
        user_agent_lower = user_agent.lower()
        
        if "chrome" in user_agent_lower and "edg" not in user_agent_lower:
            return "chrome"
        elif "firefox" in user_agent_lower:
            return "firefox"
        elif "safari" in user_agent_lower and "chrome" not in user_agent_lower:
            return "safari"
        elif "edg" in user_agent_lower:
            return "edge"
        elif "opera" in user_agent_lower:
            return "opera"
        else:
            return "other"
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address, handling proxy headers."""
        # Check for common proxy headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    async def _periodic_cleanup(self):
        """Periodically clean up old sessions."""
        now = datetime.utcnow()
        
        # Only run cleanup every hour
        if (now - self.last_cleanup).total_seconds() < 3600:
            return
        
        self.last_cleanup = now
        cutoff_time = now - timedelta(hours=self.cleanup_interval_hours)
        sessions_to_remove = []
        
        for session_id, session_data in self.active_sessions.items():
            last_activity = session_data.get('last_activity', session_data.get('created_at'))
            
            if last_activity < cutoff_time:
                # End session in database
                if session_data.get('started_in_db', False):
                    self.session_manager.end_session(
                        session_id=session_id,
                        queries_count=session_data.get('queries_count', 0),
                        interactions_count=session_data.get('interactions_count', 0),
                        pages_viewed=session_data.get('pages_viewed', 0)
                    )
                
                sessions_to_remove.append(session_id)
        
        # Remove from memory
        for session_id in sessions_to_remove:
            del self.active_sessions[session_id]
        
        if sessions_to_remove:
            logger.info(f"Cleaned up {len(sessions_to_remove)} old sessions")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get current session statistics for monitoring."""
        now = datetime.utcnow()
        active_count = 0
        total_queries = 0
        total_interactions = 0
        
        for session_data in self.active_sessions.values():
            last_activity = session_data.get('last_activity', session_data.get('created_at'))
            if (now - last_activity).total_seconds() < self.session_timeout_minutes * 60:
                active_count += 1
            
            total_queries += session_data.get('queries_count', 0)
            total_interactions += session_data.get('interactions_count', 0)
        
        return {
            'total_sessions': len(self.active_sessions),
            'active_sessions': active_count,
            'total_queries': total_queries,
            'total_interactions': total_interactions,
            'last_cleanup': self.last_cleanup.isoformat()
        }


# --- Helper Functions for Manual Session Access ---

def get_session_id_from_request(request: Request) -> Optional[str]:
    """
    Helper function to get session ID from request.
    Useful for endpoints that need to access session data manually.
    """
    return request.cookies.get("tilbudsjaegeren_session")

def get_session_data(session_id: str, middleware_instance: SessionTrackingMiddleware) -> Optional[Dict[str, Any]]:
    """
    Get session data for a specific session ID.
    """
    return middleware_instance.active_sessions.get(session_id)

# --- Global Middleware Instance ---
# This will be created when the middleware is added to the FastAPI app
