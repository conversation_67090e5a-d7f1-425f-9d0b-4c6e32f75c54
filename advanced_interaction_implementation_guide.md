# Advanced Interaction Logging Implementation Guide

## ✅ Task 7 Complete: Advanced Interaction Logging Implemented

### **What We've Built:**

#### **📊 Comprehensive Interaction Tracking**
- **Product interaction logging** - Clicks, views, position tracking
- **Catalog engagement tracking** - Downloads, view duration, source queries
- **Search behavior analysis** - Query refinements, suggestions, timing
- **UI element interactions** - Button clicks, navigation, element engagement
- **Error and performance tracking** - User-experienced issues and slow responses

#### **🎯 Backend API Endpoints**
- **POST /log-product-click** - Track product clicks with context
- **POST /log-catalog-download** - Track catalog PDF downloads
- **POST /log-query-refinement** - Track search query improvements
- **POST /log-store-filter-change** - Track store selection changes
- **POST /log-ui-interaction** - Track UI element interactions
- **POST /log-error-encounter** - Track user-experienced errors

#### **🚀 Frontend Integration Library**
- **TilbudsjaegerenTracker class** - Complete JavaScript tracking library
- **Auto-tracking setup** - Automatic detection and tracking of common elements
- **Performance monitoring** - Slow response detection and logging
- **Error tracking** - Automatic JavaScript error logging

## **Backend Implementation Details**

### **Advanced Interaction Logger Class**
```python
from advanced_interaction_logger import advanced_interaction_logger

# Product click tracking
interaction_id = advanced_interaction_logger.log_product_click(
    product_id=123,
    product_name="Coca Cola 2L",
    product_price=15.95,
    store_id=1,
    store_name="Netto",
    source_query_id=456,
    position_in_results=3,
    user_id=user.id,
    session_id=session_id
)

# Catalog download tracking
interaction_id = advanced_interaction_logger.log_catalog_download(
    catalog_id=789,
    catalog_title="Netto Ugetilbud",
    store_id=1,
    store_name="Netto",
    file_size_bytes=2048576,
    source_query_id=456,
    user_id=user.id,
    session_id=session_id
)
```

### **API Endpoint Integration**
```python
@app.post("/log-product-click")
async def log_product_click_endpoint(
    request: Request,
    interaction_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    session_id = get_session_id_from_request(request)
    
    interaction_id = log_product_click(
        product_id=interaction_data.get("product_id"),
        product_name=interaction_data.get("product_name", ""),
        product_price=interaction_data.get("product_price"),
        store_id=interaction_data.get("store_id"),
        store_name=interaction_data.get("store_name"),
        source_query_id=interaction_data.get("source_query_id"),
        position_in_results=interaction_data.get("position_in_results"),
        user_id=current_user.id if current_user else None,
        session_id=session_id
    )
    
    return {"status": "logged", "interaction_id": interaction_id}
```

## **Frontend Implementation**

### **Basic Setup**
```html
<!-- Include the tracking library -->
<script src="/static/frontend_interaction_tracker.js"></script>

<script>
// The tracker is automatically available as window.tilbudsjaegerenTracker
const tracker = window.tilbudsjaegerenTracker;

// Set current query when user searches
tracker.setCurrentQuery(queryId, queryText);
</script>
```

### **Product Click Tracking**
```html
<!-- Product grid with tracking data -->
<div class="product-grid">
    <div class="product-item" 
         data-product-id="123" 
         data-product-name="Coca Cola 2L"
         data-store-id="1"
         data-store-name="Netto">
        <h3>Coca Cola 2L</h3>
        <p>15,95 kr</p>
    </div>
</div>

<script>
// Auto-track product clicks
tracker.trackProductGrid('.product-grid');

// Or manual tracking
document.querySelector('.product-item').addEventListener('click', () => {
    tracker.logProductClick(123, 'Coca Cola 2L', {
        price: 15.95,
        storeId: 1,
        storeName: 'Netto',
        position: 1
    });
});
</script>
```

### **Catalog Download Tracking**
```html
<!-- Catalog links with tracking data -->
<div class="catalogs-container">
    <a href="/catalogs/789.pdf" 
       data-catalog-id="789"
       data-catalog-title="Netto Ugetilbud"
       data-store-id="1"
       data-store-name="Netto">
        Download Netto Catalog
    </a>
</div>

<script>
// Auto-track catalog downloads
tracker.trackCatalogLinks('.catalogs-container');

// Or manual tracking
document.querySelector('a[href*=".pdf"]').addEventListener('click', () => {
    tracker.logCatalogDownload(789, 'Netto Ugetilbud', 1, 'Netto');
});
</script>
```

### **Search Refinement Tracking**
```html
<form class="search-form">
    <input type="search" name="query" placeholder="Søg efter produkter...">
    <button type="submit">Søg</button>
</form>

<script>
// Auto-track search refinements
tracker.trackSearchForm('.search-form');

// Or manual tracking when query changes
function onQuerySubmit(newQuery) {
    if (tracker.currentQuery && tracker.currentQuery.text !== newQuery) {
        tracker.logQueryRefinement(newQuery, 'manual');
    }
    
    // Set new current query (you'll get this from your API response)
    tracker.setCurrentQuery(newQueryId, newQuery);
}
</script>
```

### **Store Filter Tracking**
```html
<div class="store-selector">
    <label><input type="checkbox" value="1"> Netto</label>
    <label><input type="checkbox" value="2"> Bilka</label>
    <label><input type="checkbox" value="3"> Føtex</label>
</div>

<script>
// Auto-track store filter changes
const storeSelector = document.querySelector('.store-selector');
tracker.trackStoreSelector(storeSelector);

// Or manual tracking
function onStoreSelectionChange(selectedStores) {
    tracker.logStoreFilterChange(selectedStores, 'manual');
}
</script>
```

### **UI Element Tracking**
```html
<button id="show-filters" class="filter-toggle">Show Filters</button>
<button id="clear-search" class="clear-button">Clear Search</button>

<script>
// Track specific UI interactions
document.getElementById('show-filters').addEventListener('click', () => {
    tracker.logUIInteraction('button', 'show-filters', 'click', {
        text: 'Show Filters'
    });
});

document.getElementById('clear-search').addEventListener('click', () => {
    tracker.logUIInteraction('button', 'clear-search', 'click', {
        text: 'Clear Search'
    });
});
</script>
```

## **Data Being Collected**

### **Product Interactions**
- ✅ **Product clicks** - Which products users click on
- ✅ **Position in results** - Where in search results they clicked
- ✅ **Source query** - Which search led to the click
- ✅ **Product context** - Name, price, store information
- ✅ **View duration** - How long they looked at product details

### **Catalog Engagement**
- ✅ **Catalog views** - Which catalogs users open
- ✅ **View duration** - How long they spend viewing catalogs
- ✅ **Download behavior** - Which catalogs they download
- ✅ **File size tracking** - Size of downloaded files
- ✅ **Source queries** - Which searches led to catalog views

### **Search Behavior**
- ✅ **Query refinements** - How users improve their searches
- ✅ **Refinement timing** - Time between query attempts
- ✅ **Refinement types** - Manual vs suggestion-based
- ✅ **Search suggestions** - Which suggestions they click
- ✅ **Query patterns** - Evolution of search terms

### **UI Interactions**
- ✅ **Button clicks** - Which UI elements users interact with
- ✅ **Navigation patterns** - How users move through the site
- ✅ **Filter usage** - How they use store filters
- ✅ **Element engagement** - Which parts of the UI get attention
- ✅ **Error encounters** - When and where users hit problems

## **Business Intelligence Insights**

### **Product Performance Analysis**
```sql
-- Most clicked products
SELECT 
    interaction_data->>'product_name' as product_name,
    COUNT(*) as click_count,
    AVG((interaction_data->>'position_in_results')::int) as avg_position
FROM user_interactions 
WHERE interaction_type = 'product_clicked'
GROUP BY interaction_data->>'product_name'
ORDER BY click_count DESC;

-- Click-through rates by position
SELECT 
    (interaction_data->>'position_in_results')::int as position,
    COUNT(*) as clicks,
    COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as click_percentage
FROM user_interactions 
WHERE interaction_type = 'product_clicked'
GROUP BY (interaction_data->>'position_in_results')::int
ORDER BY position;
```

### **Search Behavior Analysis**
```sql
-- Query refinement patterns
SELECT 
    interaction_data->>'original_query' as original_query,
    interaction_data->>'refined_query' as refined_query,
    COUNT(*) as refinement_count
FROM user_interactions 
WHERE interaction_type = 'query_refined'
GROUP BY original_query, refined_query
ORDER BY refinement_count DESC;

-- Time between query refinements
SELECT 
    AVG((interaction_data->>'time_between_queries_seconds')::int) as avg_time_between_queries,
    COUNT(*) as total_refinements
FROM user_interactions 
WHERE interaction_type = 'query_refined'
AND interaction_data->>'time_between_queries_seconds' IS NOT NULL;
```

### **Catalog Engagement Analysis**
```sql
-- Most popular catalogs
SELECT 
    target_id as catalog_id,
    interaction_data->>'catalog_title' as catalog_title,
    interaction_data->>'store_name' as store_name,
    COUNT(*) as view_count,
    AVG((interaction_data->>'view_duration_seconds')::int) as avg_view_duration
FROM user_interactions 
WHERE interaction_type = 'catalog_viewed'
GROUP BY target_id, catalog_title, store_name
ORDER BY view_count DESC;

-- Download vs view ratios
SELECT 
    store_name,
    SUM(CASE WHEN interaction_type = 'catalog_viewed' THEN 1 ELSE 0 END) as views,
    SUM(CASE WHEN interaction_type = 'catalog_downloaded' THEN 1 ELSE 0 END) as downloads,
    (SUM(CASE WHEN interaction_type = 'catalog_downloaded' THEN 1 ELSE 0 END) * 100.0 / 
     SUM(CASE WHEN interaction_type = 'catalog_viewed' THEN 1 ELSE 0 END)) as download_rate
FROM user_interactions 
WHERE interaction_type IN ('catalog_viewed', 'catalog_downloaded')
AND interaction_data->>'store_name' IS NOT NULL
GROUP BY interaction_data->>'store_name'
ORDER BY download_rate DESC;
```

## **Admin Deep-Dive Capabilities**

### **User Interaction Timeline**
```sql
-- Complete user interaction timeline
SELECT 
    created_at,
    interaction_type,
    target_type,
    target_id,
    interaction_data
FROM user_interactions 
WHERE user_id = 123
ORDER BY created_at DESC;
```

### **User Behavior Patterns**
```sql
-- User's favorite products (most clicked)
SELECT 
    interaction_data->>'product_name' as product_name,
    COUNT(*) as click_count
FROM user_interactions 
WHERE user_id = 123 
AND interaction_type = 'product_clicked'
GROUP BY interaction_data->>'product_name'
ORDER BY click_count DESC;

-- User's search refinement patterns
SELECT 
    interaction_data->>'original_query' as original_query,
    interaction_data->>'refined_query' as refined_query,
    created_at
FROM user_interactions 
WHERE user_id = 123 
AND interaction_type = 'query_refined'
ORDER BY created_at DESC;
```

## **Next Steps for Frontend Integration**

### **1. Add Tracking to Existing Pages**
- Include `frontend_interaction_tracker.js` in your main layout
- Add data attributes to product elements
- Set up auto-tracking for common interactions

### **2. Integrate with Search Results**
- Call `tracker.setCurrentQuery()` when displaying search results
- Add product click tracking to result items
- Track query refinements when users modify searches

### **3. Enhance Catalog Viewer**
- Add download tracking to PDF links
- Implement view duration tracking
- Track catalog navigation patterns

### **4. Monitor Performance**
- Enable slow response tracking
- Log user-experienced errors
- Track UI responsiveness issues

## **Advanced Interaction Logging Status: ✅ COMPLETE**

The advanced interaction logging system is now fully implemented and ready for integration. It provides comprehensive tracking of user engagement patterns, enabling deep insights into user behavior and business intelligence.

**Ready for Task 8: Create Admin API Endpoints for User Data Retrieval!** 🚀
